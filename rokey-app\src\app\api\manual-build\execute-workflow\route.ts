import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowExecutor } from '@/lib/workflow/WorkflowExecutor';
import { WorkflowNode } from '@/types/manualBuild';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { workflowId, nodes, edges, userInput, messages, stream } = body;

    if (!workflowId || !nodes || !edges) {
      return NextResponse.json({
        error: 'Missing required fields: workflowId, nodes, edges'
      }, { status: 400 });
    }

    console.log(`🚀 Executing workflow ${workflowId} for user ${user.id}`);
    console.log(`📊 Workflow contains ${nodes.length} nodes and ${edges.length} edges`);
    console.log(`🌊 Streaming enabled: ${stream === true}`);

    // Handle streaming vs non-streaming execution
    if (stream === true) {
      // Create streaming response
      const encoder = new TextEncoder();
      let accumulatedResponse = '';

      const stream = new ReadableStream({
        async start(controller) {
          try {
            // Define streaming callback
            const onStreamChunk = (chunk: string) => {
              accumulatedResponse += chunk;

              // Format as Server-Sent Event
              const sseData = `data: ${JSON.stringify({
                choices: [{
                  delta: { content: chunk },
                  finish_reason: null
                }]
              })}\n\n`;

              controller.enqueue(encoder.encode(sseData));
            };

            // Execute workflow with streaming
            const result = await workflowExecutor.executeWorkflow(
              workflowId,
              user.id,
              nodes as WorkflowNode[],
              edges,
              userInput,
              messages,
              onStreamChunk // Pass streaming callback
            );

            // Send final completion event
            const finalData = `data: ${JSON.stringify({
              choices: [{
                delta: {},
                finish_reason: 'stop'
              }],
              usage: {
                total_tokens: Math.ceil(accumulatedResponse.length / 4)
              }
            })}\n\n`;

            controller.enqueue(encoder.encode(finalData));
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();

          } catch (error) {
            console.error('Streaming workflow execution error:', error);

            const errorData = `data: ${JSON.stringify({
              error: {
                message: error instanceof Error ? error.message : 'Workflow execution failed',
                type: 'workflow_error'
              }
            })}\n\n`;

            controller.enqueue(encoder.encode(errorData));
            controller.close();
          }
        }
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control',
          'X-Accel-Buffering': 'no'
        }
      });
    } else {
      // Non-streaming execution (backward compatibility)
      const result = await workflowExecutor.executeWorkflow(
        workflowId,
        user.id,
        nodes as WorkflowNode[],
        edges,
        userInput,
        messages
      );

      return NextResponse.json({
        success: true,
        workflowId,
        result,
        executedAt: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Workflow execution error:', error);

    return NextResponse.json({
      error: 'Workflow execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflowId');

    if (workflowId) {
      // Get specific workflow execution status
      const execution = workflowExecutor.getExecutionStatus(workflowId);
      
      if (!execution) {
        return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
      }

      // Only return execution if it belongs to the authenticated user
      if (execution.userId !== user.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
      }

      return NextResponse.json(execution);
    } else {
      // Get all active executions for the user
      const allExecutions = workflowExecutor.getActiveExecutions();
      const userExecutions = allExecutions.filter(exec => exec.userId === user.id);

      return NextResponse.json({
        executions: userExecutions,
        totalActive: userExecutions.length
      });
    }

  } catch (error) {
    console.error('Get workflow execution error:', error);
    
    return NextResponse.json({
      error: 'Failed to get workflow execution',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
