/**
 * API endpoint for individual workflow share management
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowSharingService } from '@/lib/workflow/WorkflowSharingService';

interface RouteParams {
  params: Promise<{
    workflowId: string;
    shareId: string;
  }>;
}

/**
 * DELETE /api/manual-build/workflows/[workflowId]/shares/[shareId]
 * Revoke a specific share
 */
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  const { workflowId, shareId } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Revoke the share
    await workflowSharingService.revokeShare(shareId, user.id);

    return NextResponse.json({
      message: 'Share revoked successfully'
    });

  } catch (error) {
    console.error('Revoke workflow share error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/manual-build/workflows/[workflowId]/shares/[shareId]
 * Update share permissions
 */
export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  const { workflowId, shareId } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { permissionLevel } = body;

    if (!permissionLevel) {
      return NextResponse.json(
        { error: 'Permission level is required' },
        { status: 400 }
      );
    }

    // Update share permissions
    const updatedShare = await workflowSharingService.updateSharePermissions(
      shareId,
      user.id,
      permissionLevel
    );

    return NextResponse.json({
      share: updatedShare,
      message: 'Share permissions updated successfully'
    });

  } catch (error) {
    console.error('Update share permissions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
