"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_streamingUtils_ts";
exports.ids = ["_rsc_src_utils_streamingUtils_ts"];
exports.modules = {

/***/ "(rsc)/./src/utils/streamingUtils.ts":
/*!*************************************!*\
  !*** ./src/utils/streamingUtils.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   createFirstTokenTrackingStream: () => (/* binding */ createFirstTokenTrackingStream),\n/* harmony export */   estimateTokenCount: () => (/* binding */ estimateTokenCount),\n/* harmony export */   evaluatePerformance: () => (/* binding */ evaluatePerformance),\n/* harmony export */   getProviderModelFromContext: () => (/* binding */ getProviderModelFromContext),\n/* harmony export */   logStreamingPerformance: () => (/* binding */ logStreamingPerformance)\n/* harmony export */ });\n// Streaming utilities for first token tracking and performance monitoring\nfunction createFirstTokenTrackingStream(originalStream, provider, model) {\n    const reader = originalStream.getReader();\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        async start (controller) {\n            let firstTokenSent = false;\n            const streamStartTime = Date.now();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        controller.close();\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // Check if this chunk contains actual content (first token)\n                    if (!firstTokenSent && chunk.includes('delta')) {\n                        try {\n                            // Parse SSE data to check for content\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        const parsed = JSON.parse(jsonData);\n                                        if (parsed.choices?.[0]?.delta?.content) {\n                                            const firstTokenTime = Date.now() - streamStartTime;\n                                            console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model})`);\n                                            firstTokenSent = true;\n                                            break;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors for individual chunks\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                            // Ignore parsing errors, just track timing\n                            if (!firstTokenSent) {\n                                const firstTokenTime = Date.now() - streamStartTime;\n                                console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model}) [fallback detection]`);\n                                firstTokenSent = true;\n                            }\n                        }\n                    }\n                    // Forward the chunk unchanged\n                    controller.enqueue(value);\n                }\n            } catch (error) {\n                console.error(`[${provider} Stream Tracking] Error:`, error);\n                // Phase 1 Optimization: Graceful error handling for connection resets\n                if (error instanceof Error && (error.message.includes('aborted') || error.message.includes('ECONNRESET'))) {\n                    console.log(`[${provider} Stream] Connection reset detected - closing stream gracefully`);\n                    controller.close();\n                } else {\n                    controller.error(error);\n                }\n            }\n        }\n    });\n}\n// Enhanced logging for streaming performance\nfunction logStreamingPerformance(provider, model, metrics) {\n    console.log(`📊 STREAMING PERFORMANCE: ${provider}/${model}`);\n    if (metrics.timeToFirstToken !== undefined) {\n        console.log(`   ⏱️ Time to First Token: ${metrics.timeToFirstToken.toFixed(1)}ms`);\n        // Performance categories\n        if (metrics.timeToFirstToken < 500) {\n            console.log(`   ⚡ EXCELLENT first token performance`);\n        } else if (metrics.timeToFirstToken < 1000) {\n            console.log(`   ✅ GOOD first token performance`);\n        } else if (metrics.timeToFirstToken < 2000) {\n            console.log(`   ⚠️ SLOW first token performance`);\n        } else {\n            console.log(`   🐌 VERY SLOW first token performance`);\n        }\n    }\n    if (metrics.totalStreamTime !== undefined) {\n        console.log(`   🔄 Total Stream Time: ${metrics.totalStreamTime.toFixed(1)}ms`);\n    }\n    if (metrics.totalTokens !== undefined) {\n        console.log(`   🎯 Total Tokens: ${metrics.totalTokens}`);\n    }\n    if (metrics.averageTokenLatency !== undefined) {\n        console.log(`   📈 Avg Token Latency: ${metrics.averageTokenLatency.toFixed(1)}ms/token`);\n    }\n}\n// Utility to extract provider and model from request context\nfunction getProviderModelFromContext(providerName, modelId) {\n    return {\n        provider: providerName || 'unknown',\n        model: modelId || 'unknown'\n    };\n}\n// Simple token counter for rough estimation\nfunction estimateTokenCount(text) {\n    // Rough estimation: 1 token ≈ 4 characters for English text\n    // This is a simplified approach, real tokenization would be more accurate\n    return Math.ceil(text.length / 4);\n}\n// Performance thresholds for different providers\nconst PERFORMANCE_THRESHOLDS = {\n    EXCELLENT_FIRST_TOKEN: 500,\n    GOOD_FIRST_TOKEN: 1000,\n    SLOW_FIRST_TOKEN: 2000,\n    // Anything above 2000ms is considered very slow\n    EXCELLENT_TOTAL: 3000,\n    GOOD_TOTAL: 5000,\n    SLOW_TOTAL: 10000,\n    TARGET_TOKEN_LATENCY: 50\n};\n// Check if performance meets targets\nfunction evaluatePerformance(metrics) {\n    const firstTokenGrade = !metrics.timeToFirstToken ? 'very_slow' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';\n    const totalTimeGrade = !metrics.totalStreamTime ? 'very_slow' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';\n    const tokenLatencyGrade = !metrics.averageTokenLatency ? 'very_slow' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';\n    // Overall grade is the worst of the three\n    const grades = [\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade\n    ];\n    const gradeOrder = [\n        'excellent',\n        'good',\n        'slow',\n        'very_slow'\n    ];\n    const overallGrade = grades.reduce((worst, current)=>{\n        return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;\n    }, 'excellent');\n    return {\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade,\n        overallGrade\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/streamingUtils.ts\n");

/***/ })

};
;