'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  StarIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  TagIcon,
  CalendarIcon,
  UserIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template_nodes: any[];
  template_edges: any[];
  default_settings: any;
  is_official: boolean;
  created_by: string;
  download_count: number;
  rating: number;
  tags: string[];
  preview_image_url?: string;
  created_at: string;
  updated_at: string;
}

const categories = [
  'All',
  'Content Creation',
  'Data Processing',
  'Web Automation',
  'AI Workflows',
  'Business Process',
  'Research & Analysis',
  'Communication',
  'Development'
];

export default function WorkflowTemplatesPage() {
  const router = useRouter();
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<WorkflowTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState<'popular' | 'recent' | 'rating'>('popular');
  const [showFilters, setShowFilters] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<WorkflowTemplate | null>(null);

  // Load templates
  useEffect(() => {
    loadTemplates();
  }, []);

  // Filter and sort templates
  useEffect(() => {
    let filtered = templates;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Sort templates
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.download_count - a.download_count;
        case 'recent':
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        case 'rating':
          return b.rating - a.rating;
        default:
          return 0;
      }
    });

    setFilteredTemplates(filtered);
  }, [templates, searchQuery, selectedCategory, sortBy]);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/manual-build/templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUseTemplate = async (template: WorkflowTemplate) => {
    try {
      const response = await fetch('/api/manual-build/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `${template.name} (Copy)`,
          description: template.description,
          nodes: template.template_nodes,
          edges: template.template_edges,
          settings: template.default_settings,
          template_id: template.id
        }),
      });

      if (response.ok) {
        const data = await response.json();
        router.push(`/manual-build/${data.workflow.id}`);
      }
    } catch (error) {
      console.error('Failed to create workflow from template:', error);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i}>
        {i < Math.floor(rating) ? (
          <StarIconSolid className="w-4 h-4 text-yellow-400" />
        ) : (
          <StarIcon className="w-4 h-4 text-gray-400" />
        )}
      </span>
    ));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Loading templates...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#040716] text-white">
      {/* Header */}
      <div className="border-b border-gray-800 bg-gray-900/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">Workflow Templates</h1>
              <p className="text-gray-400 mt-1">
                Choose from pre-built workflows to get started quickly
              </p>
            </div>
            <button
              onClick={() => router.push('/manual-build/new')}
              className="px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors"
            >
              Create from Scratch
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="max-w-7xl mx-auto px-6 py-6">
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          {/* Search */}
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>

          {/* Sort */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"
          >
            <option value="popular">Most Popular</option>
            <option value="recent">Most Recent</option>
            <option value="rating">Highest Rated</option>
          </select>

          {/* Filters Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <FunnelIcon className="w-5 h-5" />
            Filters
          </button>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-400">
            {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''} found
          </p>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <div
              key={template.id}
              className="bg-gray-800/50 border border-gray-700 rounded-lg overflow-hidden hover:border-[#ff6b35]/50 transition-all duration-200 group"
            >
              {/* Preview Image */}
              {template.preview_image_url ? (
                <img
                  src={template.preview_image_url}
                  alt={template.name}
                  className="w-full h-48 object-cover"
                />
              ) : (
                <div className="w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                  <DocumentDuplicateIcon className="w-16 h-16 text-gray-500" />
                </div>
              )}

              {/* Content */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-white group-hover:text-[#ff6b35] transition-colors">
                    {template.name}
                  </h3>
                  {template.is_official && (
                    <span className="px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded-full border border-blue-700/30">
                      Official
                    </span>
                  )}
                </div>

                <p className="text-gray-400 text-sm mb-4 line-clamp-2">
                  {template.description}
                </p>

                {/* Tags */}
                {template.tags && template.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-4">
                    {template.tags.slice(0, 3).map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                    {template.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full">
                        +{template.tags.length - 3}
                      </span>
                    )}
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4 text-sm text-gray-400">
                    <div className="flex items-center gap-1">
                      <ArrowDownTrayIcon className="w-4 h-4" />
                      {template.download_count}
                    </div>
                    <div className="flex items-center gap-1">
                      {renderStars(template.rating)}
                      <span className="ml-1">{template.rating.toFixed(1)}</span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <button
                    onClick={() => handleUseTemplate(template)}
                    className="flex-1 px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors text-sm font-medium"
                  >
                    Use Template
                  </button>
                  <button
                    onClick={() => setPreviewTemplate(template)}
                    className="px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    <EyeIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <DocumentDuplicateIcon className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No templates found</h3>
            <p className="text-gray-400 mb-6">
              Try adjusting your search criteria or create a new workflow from scratch.
            </p>
            <button
              onClick={() => router.push('/manual-build/new')}
              className="px-6 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors"
            >
              Create New Workflow
            </button>
          </div>
        )}
      </div>

      {/* Template Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div>
                <h2 className="text-xl font-bold text-white">{previewTemplate.name}</h2>
                <p className="text-gray-400 mt-1">{previewTemplate.description}</p>
              </div>
              <button
                onClick={() => setPreviewTemplate(null)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Template Info */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Template Details</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Category:</span>
                        <span className="text-white">{previewTemplate.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Downloads:</span>
                        <span className="text-white">{previewTemplate.download_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Rating:</span>
                        <div className="flex items-center gap-1">
                          {renderStars(previewTemplate.rating)}
                          <span className="text-white ml-1">{previewTemplate.rating.toFixed(1)}</span>
                        </div>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Nodes:</span>
                        <span className="text-white">{previewTemplate.template_nodes.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Connections:</span>
                        <span className="text-white">{previewTemplate.template_edges.length}</span>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  {previewTemplate.tags && previewTemplate.tags.length > 0 && (
                    <div>
                      <h4 className="text-md font-medium text-white mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {previewTemplate.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Workflow Preview */}
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Workflow Structure</h3>
                  <div className="bg-gray-900 rounded-lg p-4 h-64 overflow-auto">
                    <div className="text-sm text-gray-300">
                      <div className="mb-2 font-medium">Nodes ({previewTemplate.template_nodes.length}):</div>
                      <ul className="space-y-1 mb-4">
                        {previewTemplate.template_nodes.map((node: any, index: number) => (
                          <li key={index} className="flex items-center gap-2">
                            <span className="w-2 h-2 bg-[#ff6b35] rounded-full"></span>
                            <span>{node.data?.label || node.type}</span>
                            <span className="text-gray-500">({node.type})</span>
                          </li>
                        ))}
                      </ul>
                      <div className="mb-2 font-medium">Connections ({previewTemplate.template_edges.length}):</div>
                      <div className="text-gray-400 text-xs">
                        {previewTemplate.template_edges.length} connections between nodes
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-700">
              <button
                onClick={() => setPreviewTemplate(null)}
                className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
              <button
                onClick={() => {
                  handleUseTemplate(previewTemplate);
                  setPreviewTemplate(null);
                }}
                className="px-6 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors"
              >
                Use This Template
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
