"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_workflow_toolExecutor_ts";
exports.ids = ["_rsc_src_lib_workflow_toolExecutor_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/workflow/toolExecutor.ts":
/*!******************************************!*\
  !*** ./src/lib/workflow/toolExecutor.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolExecutor: () => (/* binding */ ToolExecutor)\n/* harmony export */ });\n/* harmony import */ var _lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/oauth/middleware */ \"(rsc)/./src/lib/oauth/middleware.ts\");\n/* harmony import */ var _toolImplementations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toolImplementations */ \"(rsc)/./src/lib/workflow/toolImplementations.ts\");\n/* harmony import */ var _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./toolImplementations2 */ \"(rsc)/./src/lib/workflow/toolImplementations2.ts\");\n// Workflow Tool Executor\n// This handles execution of tools within Manual Build workflows\n\n\n\nclass ToolExecutor {\n    static async executeToolNode(nodeConfig, context) {\n        const startTime = Date.now();\n        try {\n            let result;\n            // Validate tool connection first\n            const connectionValidation = await (0,_lib_oauth_middleware__WEBPACK_IMPORTED_MODULE_0__.validateToolConnection)(context.userId, nodeConfig.toolType);\n            if (!connectionValidation.isValid) {\n                throw new Error(connectionValidation.error || 'Tool not connected');\n            }\n            switch(nodeConfig.toolType){\n                case 'google_drive':\n                    result = await this.executeGoogleDrive(nodeConfig, context);\n                    break;\n                case 'google_docs':\n                    result = await this.executeGoogleDocs(nodeConfig, context);\n                    break;\n                case 'google_sheets':\n                    result = await this.executeGoogleSheets(nodeConfig, context);\n                    break;\n                case 'gmail':\n                    result = await this.executeGmail(nodeConfig, context);\n                    break;\n                case 'calendar':\n                    result = await this.executeGoogleCalendar(nodeConfig, context);\n                    break;\n                case 'youtube':\n                    result = await this.executeYouTube(nodeConfig, context);\n                    break;\n                case 'notion':\n                    result = await this.executeNotion(nodeConfig, context);\n                    break;\n                case 'supabase':\n                    result = await this.executeSupabase(nodeConfig, context);\n                    break;\n                default:\n                    throw new Error(`Unknown tool type: ${nodeConfig.toolType}`);\n            }\n            const executionTime = Date.now() - startTime;\n            return {\n                success: true,\n                data: result.data,\n                toolType: nodeConfig.toolType,\n                executionTime\n            };\n        } catch (error) {\n            const executionTime = Date.now() - startTime;\n            return {\n                success: false,\n                data: null,\n                toolType: nodeConfig.toolType || 'unknown',\n                executionTime,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    // Helper method to validate tool configuration\n    static validateToolConfig(nodeConfig) {\n        const errors = [];\n        if (!nodeConfig.toolType) {\n            errors.push('Tool type is required');\n        }\n        switch(nodeConfig.toolType){\n            case 'google_drive':\n            case 'google_docs':\n            case 'google_sheets':\n            case 'notion':\n            case 'calendar':\n            case 'gmail':\n            case 'youtube':\n            case 'supabase':\n                if (!nodeConfig.isAuthenticated || nodeConfig.connectionStatus !== 'connected') {\n                    errors.push(`${nodeConfig.toolType} requires authentication and connection`);\n                }\n                break;\n            default:\n                errors.push(`Unknown tool type: ${nodeConfig.toolType}`);\n        }\n        return {\n            isValid: errors.length === 0,\n            errors\n        };\n    }\n    // Get human-readable description of what the tool will do\n    static getToolDescription(nodeConfig) {\n        switch(nodeConfig.toolType){\n            case 'google_drive':\n                return 'Access and manage Google Drive files';\n            case 'google_docs':\n                return 'Create and edit Google Documents';\n            case 'google_sheets':\n                return 'Work with Google Spreadsheets';\n            case 'zapier':\n                return 'Connect with 5000+ apps via Zapier';\n            case 'notion':\n                return 'Access Notion databases and pages';\n            case 'calendar':\n                return 'Manage calendar events and schedules';\n            case 'gmail':\n                return 'Send and manage emails';\n            case 'youtube':\n                return 'Access YouTube data and analytics';\n            case 'supabase':\n                return 'Direct database operations';\n            default:\n                return 'External tool integration';\n        }\n    }\n    // Get example usage for the tool\n    static getToolExampleUsage(nodeConfig) {\n        switch(nodeConfig.toolType){\n            case 'web_browsing':\n                return 'Example: \"search for latest AI news\" or \"find information about climate change\"';\n            case 'google_drive':\n                return 'Example: \"list files in folder\" or \"upload document\"';\n            case 'google_docs':\n                return 'Example: \"create new document\" or \"edit document content\"';\n            case 'google_sheets':\n                return 'Example: \"add row to spreadsheet\" or \"calculate sum\"';\n            case 'zapier':\n                return 'Example: \"trigger workflow\" or \"send data to app\"';\n            case 'notion':\n                return 'Example: \"create page\" or \"query database\"';\n            case 'calendar':\n                return 'Example: \"schedule meeting\" or \"check availability\"';\n            case 'gmail':\n                return 'Example: \"send email\" or \"check inbox\"';\n            case 'youtube':\n                return 'Example: \"get video stats\" or \"upload video\"';\n            case 'supabase':\n                return 'Example: \"query table\" or \"insert record\"';\n            default:\n                return 'Tool-specific input required';\n        }\n    }\n    // Check if tool is ready to use\n    static isToolReady(nodeConfig) {\n        return nodeConfig.isAuthenticated && nodeConfig.connectionStatus === 'connected';\n    }\n    // Get tool status\n    static getToolStatus(nodeConfig) {\n        if (!nodeConfig.toolType) {\n            return {\n                status: 'error',\n                message: 'No tool type selected'\n            };\n        }\n        switch(nodeConfig.toolType){\n            case 'google_drive':\n            case 'google_docs':\n            case 'google_sheets':\n            case 'notion':\n            case 'calendar':\n            case 'gmail':\n            case 'youtube':\n            case 'supabase':\n                if (!nodeConfig.isAuthenticated) {\n                    return {\n                        status: 'needs_auth',\n                        message: `${nodeConfig.toolType} requires authentication`\n                    };\n                }\n                if (nodeConfig.connectionStatus === 'expired' || nodeConfig.connectionStatus === 'revoked') {\n                    return {\n                        status: 'expired',\n                        message: `${nodeConfig.toolType} connection has expired`\n                    };\n                }\n                if (nodeConfig.connectionStatus === 'connected') {\n                    return {\n                        status: 'ready',\n                        message: `${nodeConfig.toolType} is ready to use`\n                    };\n                }\n                return {\n                    status: 'error',\n                    message: `${nodeConfig.toolType} connection error`\n                };\n            default:\n                return {\n                    status: 'error',\n                    message: 'Unknown tool type'\n                };\n        }\n    }\n    /**\n   * Execute Google Drive tool\n   */ static async executeGoogleDrive(nodeConfig, context) {\n        console.log('📁 Executing Google Drive tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            // Parse user input to determine action\n            const action = this.parseGoogleDriveAction(userInput);\n            switch(action.type){\n                case 'list_files':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDriveAPI.listFiles(userId, action.params, timeout);\n                case 'get_file':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDriveAPI.getFile(userId, action.params, timeout);\n                case 'create_file':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDriveAPI.createFile(userId, action.params, timeout);\n                case 'search_files':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDriveAPI.searchFiles(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported Google Drive action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('Google Drive tool error:', error);\n            throw new Error(`Google Drive operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute Google Docs tool\n   */ static async executeGoogleDocs(nodeConfig, context) {\n        console.log('📄 Executing Google Docs tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            const action = this.parseGoogleDocsAction(userInput);\n            switch(action.type){\n                case 'create_document':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDocsAPI.createDocument(userId, action.params, timeout);\n                case 'get_document':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDocsAPI.getDocument(userId, action.params, timeout);\n                case 'update_document':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleDocsAPI.updateDocument(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported Google Docs action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('Google Docs tool error:', error);\n            throw new Error(`Google Docs operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute Google Sheets tool\n   */ static async executeGoogleSheets(nodeConfig, context) {\n        console.log('📊 Executing Google Sheets tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            const action = this.parseGoogleSheetsAction(userInput);\n            switch(action.type){\n                case 'create_spreadsheet':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsAPI.createSpreadsheet(userId, action.params, timeout);\n                case 'get_spreadsheet':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsAPI.getSpreadsheet(userId, action.params, timeout);\n                case 'update_cells':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsAPI.updateCells(userId, action.params, timeout);\n                case 'read_range':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsAPI.readRange(userId, action.params, timeout);\n                case 'append_row':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsAPI.appendRow(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported Google Sheets action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('Google Sheets tool error:', error);\n            throw new Error(`Google Sheets operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute Gmail tool\n   */ static async executeGmail(nodeConfig, context) {\n        console.log('📧 Executing Gmail tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            const action = this.parseGmailAction(userInput);\n            switch(action.type){\n                case 'send_email':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GmailAPI.sendEmail(userId, action.params, timeout);\n                case 'list_emails':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GmailAPI.listEmails(userId, action.params, timeout);\n                case 'get_email':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GmailAPI.getEmail(userId, action.params, timeout);\n                case 'search_emails':\n                    return await _toolImplementations__WEBPACK_IMPORTED_MODULE_1__.GmailAPI.searchEmails(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported Gmail action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('Gmail tool error:', error);\n            throw new Error(`Gmail operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute Google Calendar tool\n   */ static async executeGoogleCalendar(nodeConfig, context) {\n        console.log('📅 Executing Google Calendar tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            const action = this.parseGoogleCalendarAction(userInput);\n            switch(action.type){\n                case 'create_event':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.GoogleCalendarAPI.createEvent(userId, action.params, timeout);\n                case 'list_events':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.GoogleCalendarAPI.listEvents(userId, action.params, timeout);\n                case 'get_event':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.GoogleCalendarAPI.getEvent(userId, action.params, timeout);\n                case 'update_event':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.GoogleCalendarAPI.updateEvent(userId, action.params, timeout);\n                case 'delete_event':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.GoogleCalendarAPI.deleteEvent(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported Google Calendar action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('Google Calendar tool error:', error);\n            throw new Error(`Google Calendar operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute YouTube tool\n   */ static async executeYouTube(nodeConfig, context) {\n        console.log('📺 Executing YouTube tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            const action = this.parseYouTubeAction(userInput);\n            switch(action.type){\n                case 'search_videos':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.YouTubeAPI.searchVideos(userId, action.params, timeout);\n                case 'get_video':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.YouTubeAPI.getVideo(userId, action.params, timeout);\n                case 'get_channel':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.YouTubeAPI.getChannel(userId, action.params, timeout);\n                case 'get_analytics':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.YouTubeAPI.getAnalytics(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported YouTube action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('YouTube tool error:', error);\n            throw new Error(`YouTube operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute Notion tool\n   */ static async executeNotion(nodeConfig, context) {\n        console.log('📝 Executing Notion tool');\n        const { userInput, userId } = context;\n        const timeout = nodeConfig.timeout || 30;\n        try {\n            const action = this.parseNotionAction(userInput);\n            switch(action.type){\n                case 'create_page':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.NotionAPI.createPage(userId, action.params, timeout);\n                case 'get_page':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.NotionAPI.getPage(userId, action.params, timeout);\n                case 'update_page':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.NotionAPI.updatePage(userId, action.params, timeout);\n                case 'query_database':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.NotionAPI.queryDatabase(userId, action.params, timeout);\n                case 'create_database_entry':\n                    return await _toolImplementations2__WEBPACK_IMPORTED_MODULE_2__.NotionAPI.createDatabaseEntry(userId, action.params, timeout);\n                default:\n                    throw new Error(`Unsupported Notion action: ${action.type}`);\n            }\n        } catch (error) {\n            console.error('Notion tool error:', error);\n            throw new Error(`Notion operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    /**\n   * Execute Supabase tool\n   */ static async executeSupabase(nodeConfig, context) {\n        console.log('🗄️ Executing Supabase tool');\n        const { userInput, userId } = context;\n        try {\n            // For Supabase, we'll use a simple implementation since it's the user's own database\n            const action = this.parseSupabaseAction(userInput);\n            // Note: Supabase tool would typically use the user's own Supabase instance\n            // For now, we'll return a placeholder response\n            return {\n                success: true,\n                action: action.type,\n                message: 'Supabase integration requires custom configuration',\n                data: null\n            };\n        } catch (error) {\n            console.error('Supabase tool error:', error);\n            throw new Error(`Supabase operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);\n        }\n    }\n    // Action parsers - these interpret user input and determine what API calls to make\n    static parseGoogleDriveAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('list') || input.includes('show files')) {\n            return {\n                type: 'list_files',\n                params: {\n                    limit: 10\n                }\n            };\n        }\n        if (input.includes('search')) {\n            const query = userInput.replace(/search\\s+for\\s+/i, '').replace(/search\\s+/i, '');\n            return {\n                type: 'search_files',\n                params: {\n                    query\n                }\n            };\n        }\n        if (input.includes('create') || input.includes('new file')) {\n            const name = userInput.replace(/create\\s+file\\s+/i, '').replace(/new\\s+file\\s+/i, '');\n            return {\n                type: 'create_file',\n                params: {\n                    name,\n                    content: ''\n                }\n            };\n        }\n        if (input.includes('get file') || input.includes('download')) {\n            // Extract file ID from input (simplified)\n            return {\n                type: 'get_file',\n                params: {\n                    fileId: 'extracted_file_id'\n                }\n            };\n        }\n        // Default to listing files\n        return {\n            type: 'list_files',\n            params: {\n                limit: 10\n            }\n        };\n    }\n    static parseGoogleDocsAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('create') || input.includes('new document')) {\n            const title = userInput.replace(/create\\s+document\\s+/i, '').replace(/new\\s+document\\s+/i, '') || 'Untitled Document';\n            return {\n                type: 'create_document',\n                params: {\n                    title\n                }\n            };\n        }\n        if (input.includes('get') || input.includes('open')) {\n            return {\n                type: 'get_document',\n                params: {\n                    documentId: 'extracted_doc_id'\n                }\n            };\n        }\n        if (input.includes('update') || input.includes('edit')) {\n            return {\n                type: 'update_document',\n                params: {\n                    documentId: 'extracted_doc_id',\n                    requests: []\n                }\n            };\n        }\n        // Default to creating a document\n        return {\n            type: 'create_document',\n            params: {\n                title: 'New Document'\n            }\n        };\n    }\n    static parseGoogleSheetsAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('create') || input.includes('new spreadsheet')) {\n            const title = userInput.replace(/create\\s+spreadsheet\\s+/i, '').replace(/new\\s+spreadsheet\\s+/i, '') || 'Untitled Spreadsheet';\n            return {\n                type: 'create_spreadsheet',\n                params: {\n                    title\n                }\n            };\n        }\n        if (input.includes('read') || input.includes('get range')) {\n            return {\n                type: 'read_range',\n                params: {\n                    spreadsheetId: 'extracted_sheet_id',\n                    range: 'A1:Z100'\n                }\n            };\n        }\n        if (input.includes('append') || input.includes('add row')) {\n            return {\n                type: 'append_row',\n                params: {\n                    spreadsheetId: 'extracted_sheet_id',\n                    range: 'A1',\n                    values: []\n                }\n            };\n        }\n        if (input.includes('update') || input.includes('edit cells')) {\n            return {\n                type: 'update_cells',\n                params: {\n                    spreadsheetId: 'extracted_sheet_id',\n                    range: 'A1',\n                    values: []\n                }\n            };\n        }\n        // Default to creating a spreadsheet\n        return {\n            type: 'create_spreadsheet',\n            params: {\n                title: 'New Spreadsheet'\n            }\n        };\n    }\n    static parseGmailAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('send') || input.includes('email')) {\n            // Extract email details (simplified)\n            return {\n                type: 'send_email',\n                params: {\n                    to: '<EMAIL>',\n                    subject: 'Subject from AI',\n                    body: userInput\n                }\n            };\n        }\n        if (input.includes('list') || input.includes('inbox')) {\n            return {\n                type: 'list_emails',\n                params: {\n                    maxResults: 10\n                }\n            };\n        }\n        if (input.includes('search')) {\n            const query = userInput.replace(/search\\s+for\\s+/i, '').replace(/search\\s+/i, '');\n            return {\n                type: 'search_emails',\n                params: {\n                    query,\n                    maxResults: 10\n                }\n            };\n        }\n        if (input.includes('get') || input.includes('read')) {\n            return {\n                type: 'get_email',\n                params: {\n                    messageId: 'extracted_message_id'\n                }\n            };\n        }\n        // Default to listing emails\n        return {\n            type: 'list_emails',\n            params: {\n                maxResults: 10\n            }\n        };\n    }\n    static parseGoogleCalendarAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('create') || input.includes('schedule') || input.includes('new event')) {\n            return {\n                type: 'create_event',\n                params: {\n                    summary: 'Event from AI',\n                    start: new Date().toISOString(),\n                    end: new Date(Date.now() + 3600000).toISOString() // 1 hour later\n                }\n            };\n        }\n        if (input.includes('list') || input.includes('events')) {\n            return {\n                type: 'list_events',\n                params: {\n                    maxResults: 10\n                }\n            };\n        }\n        if (input.includes('get') || input.includes('show event')) {\n            return {\n                type: 'get_event',\n                params: {\n                    eventId: 'extracted_event_id'\n                }\n            };\n        }\n        if (input.includes('update') || input.includes('edit')) {\n            return {\n                type: 'update_event',\n                params: {\n                    eventId: 'extracted_event_id'\n                }\n            };\n        }\n        if (input.includes('delete') || input.includes('cancel')) {\n            return {\n                type: 'delete_event',\n                params: {\n                    eventId: 'extracted_event_id'\n                }\n            };\n        }\n        // Default to listing events\n        return {\n            type: 'list_events',\n            params: {\n                maxResults: 10\n            }\n        };\n    }\n    static parseYouTubeAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('search')) {\n            const query = userInput.replace(/search\\s+for\\s+/i, '').replace(/search\\s+/i, '');\n            return {\n                type: 'search_videos',\n                params: {\n                    query,\n                    maxResults: 10\n                }\n            };\n        }\n        if (input.includes('get video') || input.includes('video details')) {\n            return {\n                type: 'get_video',\n                params: {\n                    videoId: 'extracted_video_id'\n                }\n            };\n        }\n        if (input.includes('channel') || input.includes('get channel')) {\n            return {\n                type: 'get_channel',\n                params: {\n                    channelId: 'extracted_channel_id'\n                }\n            };\n        }\n        if (input.includes('analytics') || input.includes('stats')) {\n            return {\n                type: 'get_analytics',\n                params: {\n                    channelId: 'extracted_channel_id',\n                    startDate: '2024-01-01',\n                    endDate: '2024-12-31'\n                }\n            };\n        }\n        // Default to searching videos\n        return {\n            type: 'search_videos',\n            params: {\n                query: userInput,\n                maxResults: 10\n            }\n        };\n    }\n    static parseNotionAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('create page') || input.includes('new page')) {\n            return {\n                type: 'create_page',\n                params: {\n                    parent: {\n                        page_id: 'parent_page_id'\n                    },\n                    properties: {\n                        title: {\n                            title: [\n                                {\n                                    text: {\n                                        content: 'New Page'\n                                    }\n                                }\n                            ]\n                        }\n                    }\n                }\n            };\n        }\n        if (input.includes('get page') || input.includes('read page')) {\n            return {\n                type: 'get_page',\n                params: {\n                    pageId: 'extracted_page_id'\n                }\n            };\n        }\n        if (input.includes('update') || input.includes('edit')) {\n            return {\n                type: 'update_page',\n                params: {\n                    pageId: 'extracted_page_id',\n                    properties: {}\n                }\n            };\n        }\n        if (input.includes('query') || input.includes('database')) {\n            return {\n                type: 'query_database',\n                params: {\n                    databaseId: 'extracted_db_id'\n                }\n            };\n        }\n        if (input.includes('create entry') || input.includes('add to database')) {\n            return {\n                type: 'create_database_entry',\n                params: {\n                    databaseId: 'extracted_db_id',\n                    properties: {}\n                }\n            };\n        }\n        // Default to creating a page\n        return {\n            type: 'create_page',\n            params: {\n                parent: {\n                    page_id: 'parent_page_id'\n                },\n                properties: {\n                    title: {\n                        title: [\n                            {\n                                text: {\n                                    content: userInput\n                                }\n                            }\n                        ]\n                    }\n                }\n            }\n        };\n    }\n    static parseSupabaseAction(userInput) {\n        const input = userInput.toLowerCase();\n        if (input.includes('query') || input.includes('select')) {\n            return {\n                type: 'query_table',\n                params: {\n                    table: 'extracted_table',\n                    query: {}\n                }\n            };\n        }\n        if (input.includes('insert') || input.includes('add')) {\n            return {\n                type: 'insert_record',\n                params: {\n                    table: 'extracted_table',\n                    data: {}\n                }\n            };\n        }\n        if (input.includes('update') || input.includes('edit')) {\n            return {\n                type: 'update_record',\n                params: {\n                    table: 'extracted_table',\n                    id: 'extracted_id',\n                    data: {}\n                }\n            };\n        }\n        if (input.includes('delete') || input.includes('remove')) {\n            return {\n                type: 'delete_record',\n                params: {\n                    table: 'extracted_table',\n                    id: 'extracted_id'\n                }\n            };\n        }\n        // Default to querying\n        return {\n            type: 'query_table',\n            params: {\n                table: 'default_table',\n                query: {}\n            }\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/workflow/toolExecutor.ts\n");

/***/ })

};
;