'use client';

import { useCallback } from 'react';
import { 
  TrashIcon, 
  DocumentDuplicateIcon, 
  LinkSlashIcon,
  Cog6ToothIcon 
} from '@heroicons/react/24/outline';

interface ContextMenuProps {
  id: string;
  top: number;
  left: number;
  right?: number;
  bottom?: number;
  type: 'node' | 'edge';
  nodeType?: string;
  onClose: () => void;
  onDelete: (id: string) => void;
  onDuplicate?: (id: string) => void;
  onConfigure?: (id: string) => void;
  onDisconnect?: (id: string) => void;
}

export default function ContextMenu({
  id,
  top,
  left,
  right,
  bottom,
  type,
  nodeType,
  onClose,
  onDelete,
  onDuplicate,
  onConfigure,
  onDisconnect
}: ContextMenuProps) {
  const handleAction = useCallback((action: () => void) => {
    action();
    onClose();
  }, [onClose]);

  // Check if node can be deleted (not core nodes)
  const canDelete = type === 'edge' || !['userRequest', 'classifier', 'output'].includes(nodeType || '');

  return (
    <>
      {/* Backdrop to close menu */}
      <div 
        className="fixed inset-0 z-40" 
        onClick={onClose}
      />
      
      {/* Context Menu */}
      <div
        className="fixed z-50 bg-gray-800 border border-gray-700 rounded-lg shadow-xl py-1 min-w-[160px]"
        style={{
          top: bottom ? undefined : top,
          left: right ? undefined : left,
          right: right ? window.innerWidth - right : undefined,
          bottom: bottom ? window.innerHeight - bottom : undefined,
        }}
      >
        {type === 'node' && (
          <>
            {onConfigure && (
              <button
                onClick={() => handleAction(() => onConfigure(id))}
                className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2"
              >
                <Cog6ToothIcon className="w-4 h-4" />
                Configure
              </button>
            )}
            
            {onDuplicate && (
              <button
                onClick={() => handleAction(() => onDuplicate(id))}
                className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2"
              >
                <DocumentDuplicateIcon className="w-4 h-4" />
                Duplicate
              </button>
            )}
            
            {canDelete && (
              <>
                <div className="border-t border-gray-700 my-1" />
                <button
                  onClick={() => handleAction(() => onDelete(id))}
                  className="w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2"
                >
                  <TrashIcon className="w-4 h-4" />
                  Delete Node
                </button>
              </>
            )}
            
            {!canDelete && (
              <>
                <div className="border-t border-gray-700 my-1" />
                <div className="px-3 py-2 text-xs text-gray-500">
                  Core nodes cannot be deleted
                </div>
              </>
            )}
          </>
        )}
        
        {type === 'edge' && (
          <>
            {onDisconnect && (
              <button
                onClick={() => handleAction(() => onDisconnect(id))}
                className="w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2"
              >
                <LinkSlashIcon className="w-4 h-4" />
                Disconnect
              </button>
            )}
            
            <div className="border-t border-gray-700 my-1" />
            <button
              onClick={() => handleAction(() => onDelete(id))}
              className="w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2"
            >
              <TrashIcon className="w-4 h-4" />
              Delete Connection
            </button>
          </>
        )}
      </div>
    </>
  );
}
