/**
 * API endpoints for Manual Build workflow templates
 * Handles template browsing, creation, and management
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { defaultTemplates } from '@/lib/templates/defaultTemplates';

/**
 * GET /api/manual-build/templates
 * Fetch all available workflow templates
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'popular';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabase
      .from('manual_build_templates')
      .select('*');

    // Apply filters
    if (category && category !== 'All') {
      query = query.eq('category', category);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,tags.cs.{${search}}`);
    }

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        query = query.order('download_count', { ascending: false });
        break;
      case 'recent':
        query = query.order('updated_at', { ascending: false });
        break;
      case 'rating':
        query = query.order('rating', { ascending: false });
        break;
      default:
        query = query.order('created_at', { ascending: false });
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: templates, error } = await query;

    if (error) {
      console.error('Error fetching templates:', error);
      return NextResponse.json(
        { error: 'Failed to fetch templates' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('manual_build_templates')
      .select('*', { count: 'exact', head: true });

    // If no templates exist, return default templates
    if (!templates || templates.length === 0) {
      const templatesWithIds = defaultTemplates.map((template, index) => ({
        ...template,
        id: `default-${index + 1}`,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      return NextResponse.json({
        templates: templatesWithIds,
        total: templatesWithIds.length,
        limit,
        offset,
        isDefault: true
      });
    }

    return NextResponse.json({
      templates: templates || [],
      total: count || 0,
      limit,
      offset
    });

  } catch (error) {
    console.error('Templates API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/manual-build/templates
 * Create a new workflow template
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      category,
      template_nodes,
      template_edges,
      default_settings,
      tags,
      preview_image_url
    } = body;

    // Validate required fields
    if (!name || !description || !category || !template_nodes || !template_edges) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create template
    const { data: template, error } = await supabase
      .from('manual_build_templates')
      .insert({
        name,
        description,
        category,
        template_nodes,
        template_edges,
        default_settings: default_settings || {},
        is_official: false, // Only admins can create official templates
        created_by: user.id,
        download_count: 0,
        rating: 0,
        tags: tags || [],
        preview_image_url
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating template:', error);
      return NextResponse.json(
        { error: 'Failed to create template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      template,
      message: 'Template created successfully'
    });

  } catch (error) {
    console.error('Template creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/manual-build/templates
 * Update template download count or rating
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    const body = await request.json();
    const { templateId, action, value } = body;

    if (!templateId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    let updateData: any = {};

    switch (action) {
      case 'increment_download':
        // Increment download count
        const { data: currentTemplate } = await supabase
          .from('manual_build_templates')
          .select('download_count')
          .eq('id', templateId)
          .single();

        if (currentTemplate) {
          updateData.download_count = (currentTemplate.download_count || 0) + 1;
        }
        break;

      case 'update_rating':
        if (typeof value !== 'number' || value < 0 || value > 5) {
          return NextResponse.json(
            { error: 'Invalid rating value' },
            { status: 400 }
          );
        }
        updateData.rating = value;
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    const { data: template, error } = await supabase
      .from('manual_build_templates')
      .update(updateData)
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      console.error('Error updating template:', error);
      return NextResponse.json(
        { error: 'Failed to update template' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      template,
      message: 'Template updated successfully'
    });

  } catch (error) {
    console.error('Template update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
