// Tool Connection Status API
// Get connection status for all tools or specific tool for the authenticated user

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { getUserToolConnections } from '@/lib/oauth/tokenManager';
import { TOOL_DISPLAY_NAMES, TOOL_ICONS, TOOL_DESCRIPTIONS } from '@/lib/oauth/config';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 TOOL STATUS: Getting tool connection status');
    
    // Get authenticated user
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('🔐 TOOL STATUS: User not authenticated:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get tool type from query params (optional)
    const { searchParams } = new URL(request.url);
    const toolType = searchParams.get('tool');
    
    // Get all tool connections for the user
    const connections = await getUserToolConnections(user.id);
    
    if (toolType) {
      // Return status for specific tool
      const connection = connections.find(conn => conn.tool_type === toolType);
      
      const toolStatus = {
        tool_type: toolType,
        display_name: TOOL_DISPLAY_NAMES[toolType] || toolType,
        icon: TOOL_ICONS[toolType] || '🔧',
        description: TOOL_DESCRIPTIONS[toolType] || 'External tool integration',
        is_connected: !!connection,
        connection_status: connection?.connection_status || 'disconnected',
        provider_user_email: connection?.provider_user_email,
        provider_user_name: connection?.provider_user_name,
        last_used_at: connection?.last_used_at,
        connected_at: connection?.created_at
      };
      
      console.log(`🔐 TOOL STATUS: Status for ${toolType}:`, toolStatus.is_connected);
      return NextResponse.json(toolStatus);
    }
    
    // Return status for all tools
    const allTools = Object.keys(TOOL_DISPLAY_NAMES);
    const toolStatuses = allTools.map(tool => {
      const connection = connections.find(conn => conn.tool_type === tool);
      
      return {
        tool_type: tool,
        display_name: TOOL_DISPLAY_NAMES[tool],
        icon: TOOL_ICONS[tool],
        description: TOOL_DESCRIPTIONS[tool],
        is_connected: !!connection,
        connection_status: connection?.connection_status || 'disconnected',
        provider_user_email: connection?.provider_user_email,
        provider_user_name: connection?.provider_user_name,
        last_used_at: connection?.last_used_at,
        connected_at: connection?.created_at
      };
    });
    
    console.log(`🔐 TOOL STATUS: Retrieved status for ${toolStatuses.length} tools`);
    
    return NextResponse.json({
      tools: toolStatuses,
      connected_count: connections.length,
      total_count: allTools.length
    });
    
  } catch (error) {
    console.error('🔐 TOOL STATUS: Error getting tool status:', error);
    return NextResponse.json(
      { error: 'Failed to get tool status' },
      { status: 500 }
    );
  }
}

// Disconnect a tool
export async function DELETE(request: NextRequest) {
  try {
    console.log('🔐 TOOL STATUS: Disconnecting tool');
    
    // Get authenticated user
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('🔐 TOOL STATUS: User not authenticated:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get tool type from query params
    const { searchParams } = new URL(request.url);
    const toolType = searchParams.get('tool');
    
    if (!toolType) {
      return NextResponse.json(
        { error: 'Tool type is required' },
        { status: 400 }
      );
    }
    
    console.log(`🔐 TOOL STATUS: Disconnecting ${toolType} for user ${user.id}`);
    
    // Delete the connection
    const { error: deleteError } = await supabase
      .from('tool_oauth_connections')
      .delete()
      .eq('user_id', user.id)
      .eq('tool_type', toolType);
    
    if (deleteError) {
      console.error('🔐 TOOL STATUS: Error disconnecting tool:', deleteError);
      return NextResponse.json(
        { error: 'Failed to disconnect tool' },
        { status: 500 }
      );
    }
    
    console.log(`🔐 TOOL STATUS: Successfully disconnected ${toolType}`);
    
    return NextResponse.json({
      success: true,
      message: `${TOOL_DISPLAY_NAMES[toolType] || toolType} disconnected successfully`
    });
    
  } catch (error) {
    console.error('🔐 TOOL STATUS: Error disconnecting tool:', error);
    return NextResponse.json(
      { error: 'Failed to disconnect tool' },
      { status: 500 }
    );
  }
}
