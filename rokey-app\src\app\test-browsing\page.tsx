'use client';

import { useState } from 'react';
import { WebBrowsingTool } from '@/lib/tools/webBrowsing';

export default function TestBrowsingPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<any>(null);

  const testSearch = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await WebBrowsingTool.search('when is the next world cup', {
        searchEngine: 'google',
        extractionType: 'search'
      });
      setResult(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testNavigate = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await WebBrowsingTool.navigate('https://example.com', {
        extractionType: 'content'
      });
      setResult(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const testScreenshot = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await WebBrowsingTool.takeScreenshot('https://example.com');
      setResult(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const checkStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      const statusResponse = await WebBrowsingTool.getStatus();
      setStatus(statusResponse);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#040716] text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Web Browsing Tool Test</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Test Actions</h2>
            
            <button
              onClick={testSearch}
              disabled={loading}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              {loading ? 'Loading...' : 'Test Search (World Cup)'}
            </button>
            
            <button
              onClick={testNavigate}
              disabled={loading}
              className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              {loading ? 'Loading...' : 'Test Navigate (example.com)'}
            </button>
            
            <button
              onClick={testScreenshot}
              disabled={loading}
              className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              {loading ? 'Loading...' : 'Test Screenshot (example.com)'}
            </button>
            
            <button
              onClick={checkStatus}
              disabled={loading}
              className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              {loading ? 'Loading...' : 'Check Service Status'}
            </button>
          </div>
          
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Service Status</h2>
            {status && (
              <div className="bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(status, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-900/50 border border-red-500 rounded-lg">
            <h3 className="text-lg font-semibold text-red-400 mb-2">Error</h3>
            <p className="text-red-300">{error}</p>
          </div>
        )}

        {result && (
          <div className="bg-gray-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">Result</h3>
            <div className="space-y-4">
              <div>
                <span className="text-gray-400">Action:</span>
                <span className="ml-2 text-green-400">{result.action}</span>
              </div>
              <div>
                <span className="text-gray-400">Timestamp:</span>
                <span className="ml-2">{result.timestamp}</span>
              </div>
              <div>
                <span className="text-gray-400">Data:</span>
                <pre className="mt-2 p-4 bg-gray-900 rounded text-sm overflow-auto max-h-96">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
