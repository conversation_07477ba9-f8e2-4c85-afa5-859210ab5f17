/**
 * Advanced Error Recovery Service for Manual Build Workflows
 * Provides intelligent error handling, retry mechanisms, and recovery strategies
 */

import { WorkflowNode } from '@/types/manualBuild';
import { emitWorkflowEvent } from '@/lib/websocket/WorkflowWebSocketServer';

export interface ErrorContext {
  nodeId: string;
  nodeType: string;
  nodeLabel?: string;
  error: Error;
  attempt: number;
  maxRetries: number;
  workflowId: string;
  userId: string;
  executionId?: string;
  input?: any;
  previousErrors?: ErrorContext[];
}

export interface RecoveryStrategy {
  type: 'retry' | 'fallback' | 'skip' | 'manual' | 'alternative_path';
  description: string;
  action: (context: ErrorContext) => Promise<any>;
  condition?: (context: ErrorContext) => boolean;
  priority: number;
}

export interface ErrorRecoveryResult {
  success: boolean;
  result?: any;
  strategy?: RecoveryStrategy;
  message: string;
  shouldContinue: boolean;
  newNodeId?: string; // For alternative path strategy
}

export class ErrorRecoveryService {
  private recoveryStrategies: Map<string, RecoveryStrategy[]> = new Map();
  private errorHistory: Map<string, ErrorContext[]> = new Map();

  constructor() {
    this.initializeDefaultStrategies();
  }

  /**
   * Initialize default recovery strategies for different node types
   */
  private initializeDefaultStrategies(): void {
    // Provider node strategies
    this.addRecoveryStrategy('provider', {
      type: 'fallback',
      description: 'Switch to fallback AI provider',
      priority: 1,
      condition: (context) => context.attempt <= 2,
      action: async (context) => {
        // Try fallback provider if configured
        const node = context as any;
        if (node.config?.fallbackProvider) {
          console.log(`🔄 Switching to fallback provider: ${node.config.fallbackProvider.providerId}`);
          return await this.executeWithFallbackProvider(context);
        }
        throw new Error('No fallback provider configured');
      }
    });

    this.addRecoveryStrategy('provider', {
      type: 'retry',
      description: 'Retry with exponential backoff',
      priority: 2,
      condition: (context) => context.attempt <= context.maxRetries,
      action: async (context) => {
        const delay = Math.min(1000 * Math.pow(2, context.attempt - 1), 10000);
        console.log(`⏳ Retrying in ${delay}ms (attempt ${context.attempt}/${context.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return await this.retryOriginalOperation(context);
      }
    });

    // Browsing node strategies
    this.addRecoveryStrategy('browsing', {
      type: 'retry',
      description: 'Retry browsing with different strategy',
      priority: 1,
      condition: (context) => context.attempt <= 3,
      action: async (context) => {
        console.log(`🌐 Retrying browsing operation with alternative approach`);
        return await this.retryBrowsingWithFallback(context);
      }
    });

    // Router node strategies
    this.addRecoveryStrategy('centralRouter', {
      type: 'alternative_path',
      description: 'Route to alternative AI provider',
      priority: 1,
      condition: (context) => this.hasAlternativeProviders(context),
      action: async (context) => {
        console.log(`🔀 Routing to alternative provider`);
        return await this.routeToAlternativeProvider(context);
      }
    });

    // Memory node strategies
    this.addRecoveryStrategy('memory', {
      type: 'skip',
      description: 'Continue without memory context',
      priority: 1,
      condition: () => true,
      action: async (context) => {
        console.log(`🧠 Continuing without memory context`);
        return { skipMemory: true, result: context.input };
      }
    });

    // Generic strategies for all nodes
    this.addGenericRecoveryStrategies();
  }

  /**
   * Add generic recovery strategies that apply to all node types
   */
  private addGenericRecoveryStrategies(): void {
    const genericStrategies: RecoveryStrategy[] = [
      {
        type: 'manual',
        description: 'Request manual intervention',
        priority: 10,
        condition: (context) => context.attempt > context.maxRetries,
        action: async (context) => {
          console.log(`👤 Requesting manual intervention for ${context.nodeType} node`);
          await this.requestManualIntervention(context);
          throw new Error('Manual intervention required');
        }
      }
    ];

    // Add generic strategies to all node types
    const nodeTypes = ['provider', 'browsing', 'centralRouter', 'memory', 'planner', 'classifier', 'tool'];
    nodeTypes.forEach(nodeType => {
      genericStrategies.forEach(strategy => {
        this.addRecoveryStrategy(nodeType, strategy);
      });
    });
  }

  /**
   * Add a recovery strategy for a specific node type
   */
  addRecoveryStrategy(nodeType: string, strategy: RecoveryStrategy): void {
    if (!this.recoveryStrategies.has(nodeType)) {
      this.recoveryStrategies.set(nodeType, []);
    }
    this.recoveryStrategies.get(nodeType)!.push(strategy);
    
    // Sort by priority (lower number = higher priority)
    this.recoveryStrategies.get(nodeType)!.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Attempt to recover from an error using available strategies
   */
  async recoverFromError(context: ErrorContext): Promise<ErrorRecoveryResult> {
    console.log(`🚨 Error recovery initiated for ${context.nodeType} node: ${context.error.message}`);
    
    // Store error in history
    this.addErrorToHistory(context);

    // Emit error event
    emitWorkflowEvent(
      context.workflowId,
      context.userId,
      'node_failed',
      {
        nodeId: context.nodeId,
        nodeType: context.nodeType,
        nodeLabel: context.nodeLabel,
        error: context.error.message,
        attempt: context.attempt,
        maxRetries: context.maxRetries
      },
      context.executionId
    );

    // Get available strategies for this node type
    const strategies = this.recoveryStrategies.get(context.nodeType) || [];
    
    for (const strategy of strategies) {
      if (!strategy.condition || strategy.condition(context)) {
        try {
          console.log(`🔧 Attempting recovery strategy: ${strategy.description}`);
          
          const result = await strategy.action(context);
          
          // Emit recovery success event
          emitWorkflowEvent(
            context.workflowId,
            context.userId,
            'log_message',
            {
              level: 'success',
              message: `Recovery successful: ${strategy.description}`,
              nodeId: context.nodeId,
              strategy: strategy.type
            },
            context.executionId
          );

          return {
            success: true,
            result,
            strategy,
            message: `Recovery successful using strategy: ${strategy.description}`,
            shouldContinue: true,
            newNodeId: strategy.type === 'alternative_path' ? result.newNodeId : undefined
          };

        } catch (recoveryError) {
          console.log(`❌ Recovery strategy failed: ${strategy.description}`, recoveryError);
          
          // Continue to next strategy
          continue;
        }
      }
    }

    // All recovery strategies failed
    emitWorkflowEvent(
      context.workflowId,
      context.userId,
      'workflow_failed',
      {
        error: 'All recovery strategies exhausted',
        nodeId: context.nodeId,
        originalError: context.error.message
      },
      context.executionId
    );

    return {
      success: false,
      message: `All recovery strategies exhausted for ${context.nodeType} node`,
      shouldContinue: false
    };
  }

  /**
   * Add error to history for pattern analysis
   */
  private addErrorToHistory(context: ErrorContext): void {
    const key = `${context.workflowId}-${context.nodeId}`;
    if (!this.errorHistory.has(key)) {
      this.errorHistory.set(key, []);
    }
    this.errorHistory.get(key)!.push(context);
    
    // Keep only last 10 errors per node
    const history = this.errorHistory.get(key)!;
    if (history.length > 10) {
      history.splice(0, history.length - 10);
    }
  }

  /**
   * Execute with fallback provider
   */
  private async executeWithFallbackProvider(context: ErrorContext): Promise<any> {
    // Implementation would depend on the specific provider execution logic
    // This is a placeholder for the actual fallback execution
    return { fallbackUsed: true, result: 'Fallback provider response' };
  }

  /**
   * Retry original operation
   */
  private async retryOriginalOperation(context: ErrorContext): Promise<any> {
    // Implementation would retry the original operation
    // This is a placeholder for the actual retry logic
    return { retried: true, result: 'Retry successful' };
  }

  /**
   * Retry browsing with fallback strategy
   */
  private async retryBrowsingWithFallback(context: ErrorContext): Promise<any> {
    // Implementation would use alternative browsing approach
    return { browsingFallback: true, result: 'Alternative browsing successful' };
  }

  /**
   * Check if alternative providers are available
   */
  private hasAlternativeProviders(context: ErrorContext): boolean {
    // Implementation would check for available alternative providers
    return true; // Placeholder
  }

  /**
   * Route to alternative provider
   */
  private async routeToAlternativeProvider(context: ErrorContext): Promise<any> {
    // Implementation would route to an alternative provider
    return { alternativeRoute: true, result: 'Alternative provider used' };
  }

  /**
   * Request manual intervention
   */
  private async requestManualIntervention(context: ErrorContext): Promise<void> {
    // Emit event for manual intervention request
    emitWorkflowEvent(
      context.workflowId,
      context.userId,
      'log_message',
      {
        level: 'warning',
        message: `Manual intervention requested for ${context.nodeType} node`,
        nodeId: context.nodeId,
        error: context.error.message,
        requiresAction: true
      },
      context.executionId
    );
  }

  /**
   * Get error statistics for a workflow
   */
  getErrorStatistics(workflowId: string): any {
    const workflowErrors = Array.from(this.errorHistory.entries())
      .filter(([key]) => key.startsWith(workflowId))
      .flatMap(([_, errors]) => errors);

    return {
      totalErrors: workflowErrors.length,
      errorsByNode: this.groupErrorsByNode(workflowErrors),
      errorsByType: this.groupErrorsByType(workflowErrors),
      recoverySuccessRate: this.calculateRecoverySuccessRate(workflowErrors)
    };
  }

  private groupErrorsByNode(errors: ErrorContext[]): Record<string, number> {
    return errors.reduce((acc, error) => {
      acc[error.nodeId] = (acc[error.nodeId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private groupErrorsByType(errors: ErrorContext[]): Record<string, number> {
    return errors.reduce((acc, error) => {
      acc[error.nodeType] = (acc[error.nodeType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private calculateRecoverySuccessRate(errors: ErrorContext[]): number {
    if (errors.length === 0) return 100;
    // This would calculate based on actual recovery outcomes
    return 85; // Placeholder
  }
}

// Export singleton instance
export const errorRecoveryService = new ErrorRecoveryService();
