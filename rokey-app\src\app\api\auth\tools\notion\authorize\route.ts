// Notion OAuth Authorization Endpoint
// Initiates OAuth flow for Notion integration

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { generateAuthUrl } from '@/lib/oauth/config';
import crypto from 'crypto';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 NOTION OAUTH: Authorization request started');
    
    // Get authenticated user
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('🔐 NOTION OAUTH: User not authenticated:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get return URL from query params
    const { searchParams } = new URL(request.url);
    const returnUrl = searchParams.get('returnUrl') || '/manual-build';
    
    console.log(`🔐 NOTION OAUTH: Authorizing Notion for user ${user.id}`);
    
    // Generate state parameter for CSRF protection
    const state = crypto.randomBytes(32).toString('hex');
    
    // Store state in database for verification
    const { error: stateError } = await supabase
      .from('oauth_states')
      .insert({
        state,
        user_id: user.id,
        tool_type: 'notion',
        return_url: returnUrl,
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
      });
    
    if (stateError) {
      console.error('🔐 NOTION OAUTH: Error storing state:', stateError);
      // Continue anyway, state verification is optional
    }
    
    // Generate authorization URL
    const authUrl = generateAuthUrl('notion', state);
    if (!authUrl) {
      console.error('🔐 NOTION OAUTH: Failed to generate auth URL');
      return NextResponse.json(
        { error: 'OAuth configuration error' },
        { status: 500 }
      );
    }
    
    console.log('🔐 NOTION OAUTH: Redirecting to Notion authorization');
    
    // Return the authorization URL for client-side redirect
    return NextResponse.json({
      authUrl,
      state,
      toolType: 'notion'
    });
    
  } catch (error) {
    console.error('🔐 NOTION OAUTH: Authorization error:', error);
    return NextResponse.json(
      { error: 'OAuth authorization failed' },
      { status: 500 }
    );
  }
}

// Handle POST requests for programmatic authorization
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { returnUrl } = body;
    
    // Create a new request with query parameters
    const url = new URL(request.url);
    if (returnUrl) {
      url.searchParams.set('returnUrl', returnUrl);
    }
    
    const newRequest = new NextRequest(url, {
      method: 'GET',
      headers: request.headers
    });
    
    return GET(newRequest);
  } catch (error) {
    console.error('🔐 NOTION OAUTH: POST authorization error:', error);
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    );
  }
}
