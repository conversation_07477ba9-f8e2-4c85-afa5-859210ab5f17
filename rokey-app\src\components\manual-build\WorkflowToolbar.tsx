'use client';

import { useState } from 'react';
import { 
  ArrowLeftIcon,
  PlayIcon,
  DocumentIcon,
  Cog6ToothIcon,
  ShareIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { ManualBuildWorkflow } from '@/types/manualBuild';

interface WorkflowToolbarProps {
  workflow: ManualBuildWorkflow | null;
  isDirty: boolean;
  isSaving: boolean;
  onSave: () => void;
  onExecute: () => void;
  onBack: () => void;
  onShare?: () => void;
}

export default function WorkflowToolbar({
  workflow,
  isDirty,
  isSaving,
  onSave,
  onExecute,
  onBack,
  onShare
}: WorkflowToolbarProps) {
  const [showSettings, setShowSettings] = useState(false);

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm border-b border-gray-700/50 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left Section */}
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50"
            title="Back to workflows"
          >
            <ArrowLeftIcon className="w-5 h-5" />
          </button>
          
          <div className="h-6 w-px bg-gray-700" />
          
          <div>
            <h1 className="text-lg font-semibold text-white">
              {workflow?.name || 'New Workflow'}
            </h1>
            <div className="flex items-center gap-2 text-sm text-gray-400">
              {isDirty && (
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-orange-500 rounded-full" />
                  Unsaved changes
                </span>
              )}
              {workflow?.updated_at && (
                <span className="flex items-center gap-1">
                  <ClockIcon className="w-3 h-3" />
                  Last saved {new Date(workflow.updated_at).toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Center Section - Quick Actions */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50"
            title="Workflow settings"
          >
            <Cog6ToothIcon className="w-5 h-5" />
          </button>
          
          <button
            onClick={onShare}
            disabled={!workflow?.id || !onShare}
            className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Share workflow"
          >
            <ShareIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-3">
          <button
            onClick={onSave}
            disabled={isSaving || !isDirty}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
              isDirty && !isSaving
                ? 'bg-blue-600 hover:bg-blue-500 text-white'
                : 'bg-gray-700 text-gray-400 cursor-not-allowed'
            }`}
          >
            <DocumentIcon className="w-4 h-4" />
            {isSaving ? 'Saving...' : 'Save'}
          </button>
          
          <button
            onClick={() => {
              if (workflow?.id) {
                window.open(`/playground/workflows`, '_blank');
              } else {
                alert('Please save the workflow first to test it in the playground');
              }
            }}
            className="bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
          >
            <PlayIcon className="w-4 h-4" />
            Test in Playground
          </button>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50">
          <h3 className="text-white font-medium mb-3">Workflow Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Max Execution Time
              </label>
              <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]">
                <option value="300">5 minutes</option>
                <option value="600">10 minutes</option>
                <option value="1800">30 minutes</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Retry Count
              </label>
              <select className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]">
                <option value="1">1 retry</option>
                <option value="3">3 retries</option>
                <option value="5">5 retries</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Options
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"
                    defaultChecked
                  />
                  <span className="ml-2 text-sm text-gray-300">Enable memory</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"
                    defaultChecked
                  />
                  <span className="ml-2 text-sm text-gray-300">Enable streaming</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
