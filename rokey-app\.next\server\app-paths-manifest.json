{"/api/workflow/stream/[workflowId]/route": "app/api/workflow/stream/[workflowId]/route.js", "/api/chat/messages/route": "app/api/chat/messages/route.js", "/api/manual-build/execute-workflow/route": "app/api/manual-build/execute-workflow/route.js", "/api/stripe/subscription-status/route": "app/api/stripe/subscription-status/route.js", "/api/workflows/route": "app/api/workflows/route.js", "/api/keys/route": "app/api/keys/route.js", "/page": "app/page.js", "/dashboard/page": "app/dashboard/page.js", "/manual-build/page": "app/manual-build/page.js", "/playground/page": "app/playground/page.js"}