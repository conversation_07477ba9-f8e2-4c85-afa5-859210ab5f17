"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_providers_executeProviderRequest_ts";
exports.ids = ["_rsc_src_lib_providers_executeProviderRequest_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/providers/executeProviderRequest.ts":
/*!*****************************************************!*\
  !*** ./src/lib/providers/executeProviderRequest.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoKeyChatCompletionRequestSchema: () => (/* binding */ RoKeyChatCompletionRequestSchema),\n/* harmony export */   executeProviderRequest: () => (/* binding */ executeProviderRequest)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n/**\r\n * Provider request execution utility\r\n *\r\n * This module contains the core logic for executing requests to various LLM providers\r\n * using RouKey's BYOK (Bring Your Own Key) system.\r\n */ \n\n// Define the schema here to avoid circular imports\nconst RoKeyChatCompletionRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    custom_api_config_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    messages: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()),\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    temperature: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    max_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    stream: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n    specific_api_key_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\n// Phase 1 Optimization: Aggressive timeout configuration for sub-500ms latency\nconst TIMEOUT_CONFIG = {\n    CLASSIFICATION: 3000,\n    LLM_REQUEST: 4000,\n    CONNECTION: 1000,\n    SOCKET: 800,\n    GOOGLE_CLASSIFICATION: 4000,\n    GOOGLE_ORCHESTRATION: 15000\n};\n// Response caching\nconst responseCache = new Map();\nconst RESPONSE_CACHE_TTL = 2 * 60 * 1000; // 2 minutes for LLM responses\nconst MAX_CACHE_SIZE = 1000; // Prevent memory bloat\n// Cache cleanup function\nfunction cleanupResponseCache() {\n    if (responseCache.size > MAX_CACHE_SIZE) {\n        const entries = Array.from(responseCache.entries());\n        entries.sort((a, b)=>a[1].timestamp - b[1].timestamp);\n        // Remove oldest 20% of entries\n        const toRemove = Math.floor(entries.length * 0.2);\n        for(let i = 0; i < toRemove; i++){\n            responseCache.delete(entries[i][0]);\n        }\n    }\n}\n// Generate cache key for LLM responses\nfunction generateResponseCacheKey(messages, model, temperature) {\n    const messageText = messages.map((m)=>`${m.role}:${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('|');\n    const cacheInput = `${model}|${messageText}|${temperature || 0}`;\n    return crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash('md5').update(cacheInput).digest('hex');\n}\n// Enhanced fetch with timeouts and retry logic\nasync function robustFetch(url, options, maxRetries = 3, timeoutMs, isOrchestration = false) {\n    let lastError;\n    // Determine timeout based on context and provider\n    let requestTimeout = timeoutMs;\n    if (!requestTimeout) {\n        if (url.includes('generativelanguage.googleapis.com')) {\n            // Google needs more time in orchestration context\n            requestTimeout = isOrchestration ? TIMEOUT_CONFIG.GOOGLE_ORCHESTRATION : TIMEOUT_CONFIG.GOOGLE_CLASSIFICATION;\n        } else {\n            requestTimeout = TIMEOUT_CONFIG.LLM_REQUEST;\n        }\n    }\n    for(let attempt = 0; attempt <= maxRetries; attempt++){\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), requestTimeout);\n            const response = await fetch(url, {\n                ...options,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            return response;\n        } catch (error) {\n            lastError = error;\n            console.warn(`[robustFetch] Attempt ${attempt + 1}/${maxRetries + 1} failed for ${url}:`, error.message);\n            if (attempt < maxRetries) {\n                const delay = Math.min(1000 * Math.pow(2, attempt), 5000);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n            }\n        }\n    }\n    throw lastError;\n}\n// Get direct provider model ID\nfunction getDirectProviderModelId(modelIdInDb, providerName) {\n    if (!modelIdInDb) return null;\n    // For OpenRouter, return the full model ID\n    if (providerName.toLowerCase() === 'openrouter') {\n        return modelIdInDb;\n    }\n    // For other providers, extract the model name after the prefix\n    const parts = modelIdInDb.split('/');\n    return parts.length > 1 ? parts[parts.length - 1] : modelIdInDb;\n}\n// Main provider request execution function\nasync function executeProviderRequest(providerName, modelIdInDb, apiKeyToUse, requestPayload) {\n    let p_llmRequestTimestamp = null;\n    let p_llmResponseTimestamp = new Date();\n    let p_status = undefined;\n    let p_error = undefined;\n    let p_response = undefined;\n    let p_responseData = undefined;\n    let p_responseHeaders = undefined;\n    // Check response cache for non-streaming requests\n    if (!requestPayload.stream && requestPayload.messages && modelIdInDb) {\n        const cacheKey = generateResponseCacheKey(requestPayload.messages, modelIdInDb, requestPayload.temperature);\n        const cached = responseCache.get(cacheKey);\n        if (cached && Date.now() - cached.timestamp < RESPONSE_CACHE_TTL) {\n            console.log(`[ResponseCache] Cache HIT for ${providerName}/${modelIdInDb} - returning cached response`);\n            return {\n                success: true,\n                response: undefined,\n                responseData: cached.response,\n                responseHeaders: new Headers({\n                    'x-rokey-cache': 'hit'\n                }),\n                status: 200,\n                error: null,\n                llmRequestTimestamp: new Date(),\n                llmResponseTimestamp: new Date()\n            };\n        } else {\n            console.log(`[ResponseCache] Cache MISS for ${providerName}/${modelIdInDb} - proceeding with API call`);\n        }\n    }\n    // Enhanced fetch configuration with better error handling\n    const fetchOptions = {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json',\n            'Connection': 'keep-alive',\n            'Keep-Alive': 'timeout=30, max=100',\n            'User-Agent': 'RoKey/1.0 (Performance-Optimized)',\n            'Accept': 'application/json',\n            'Cache-Control': 'no-cache'\n        }\n    };\n    try {\n        // Determine the actual model ID to be used for the provider API call\n        const actualModelIdForProvider = getDirectProviderModelId(modelIdInDb, providerName || '');\n        // OpenRouter uses the modelIdInDb directly as its `model` parameter, which might be like \"google/gemini-pro\"\n        // Other providers expect just the part after the prefix, e.g., \"gemini-pro\"\n        const effectiveModelId = providerName?.toLowerCase() === 'openrouter' ? modelIdInDb : actualModelIdForProvider;\n        if (!effectiveModelId) {\n            throw {\n                message: `Effective model ID is missing for provider ${providerName} (DB Model: ${modelIdInDb})`,\n                status: 500,\n                internal: true\n            };\n        }\n        console.log(`[executeProviderRequest] Calling Provider: ${providerName}, Model: ${effectiveModelId}`);\n        p_llmRequestTimestamp = new Date();\n        if (providerName?.toLowerCase() === 'openai') {\n            const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;\n            const payload = {\n                ...providerAPIPayload,\n                model: effectiveModelId,\n                messages: requestPayload.messages,\n                stream: requestPayload.stream\n            };\n            Object.keys(payload).forEach((key)=>payload[key] === undefined && delete payload[key]);\n            // Use clean options object (avoid problematic headers)\n            const openaiOptions = {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKeyToUse}`,\n                    'User-Agent': 'RoKey/1.0',\n                    'Origin': 'https://rokey.app',\n                    'Cache-Control': 'no-cache'\n                },\n                body: JSON.stringify(payload)\n            };\n            console.log(`[OpenAI] Attempting connection to OpenAI API...`);\n            const rawResponse = await robustFetch('https://api.openai.com/v1/chat/completions', openaiOptions);\n            p_llmResponseTimestamp = new Date();\n            p_status = rawResponse.status;\n            p_responseHeaders = rawResponse.headers;\n            if (!rawResponse.ok) {\n                const err = await rawResponse.json().catch(()=>({\n                        error: {\n                            message: rawResponse.statusText\n                        }\n                    }));\n                p_error = {\n                    message: `OpenAI Error: ${err?.error?.message || rawResponse.statusText}`,\n                    status: rawResponse.status,\n                    provider_error: err\n                };\n                throw p_error;\n            }\n            if (requestPayload.stream) {\n                if (!rawResponse.body) {\n                    p_error = {\n                        message: 'OpenAI stream body null',\n                        status: 500\n                    };\n                    throw p_error;\n                }\n                p_response = rawResponse; // Return raw stream response (already SSE)\n                p_responseData = {\n                    note: \"streamed\"\n                }; // For logging consistency\n            } else {\n                p_responseData = await rawResponse.json();\n            }\n        } else if (providerName?.toLowerCase() === 'openrouter') {\n            const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;\n            const payload = {\n                ...providerAPIPayload,\n                model: effectiveModelId,\n                messages: requestPayload.messages,\n                stream: requestPayload.stream,\n                usage: {\n                    include: true\n                } // Enable OpenRouter usage accounting for cost tracking\n            };\n            Object.keys(payload).forEach((key)=>payload[key] === undefined && delete payload[key]);\n            // Use clean options object like Google (avoid problematic headers)\n            const openrouterOptions = {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKeyToUse}`,\n                    'HTTP-Referer': 'https://rokey.app',\n                    'X-Title': 'RoKey',\n                    'User-Agent': 'RoKey/1.0',\n                    'Origin': 'https://rokey.app',\n                    'Cache-Control': 'no-cache'\n                },\n                body: JSON.stringify(payload)\n            };\n            console.log(`[OpenRouter] Attempting connection to OpenRouter API...`);\n            const rawResponse = await robustFetch('https://openrouter.ai/api/v1/chat/completions', openrouterOptions);\n            p_llmResponseTimestamp = new Date();\n            p_status = rawResponse.status;\n            p_responseHeaders = rawResponse.headers;\n            if (!rawResponse.ok) {\n                const err = await rawResponse.json().catch(()=>({\n                        error: {\n                            message: rawResponse.statusText\n                        }\n                    }));\n                p_error = {\n                    message: `OpenRouter Error: ${err?.error?.message || rawResponse.statusText}`,\n                    status: rawResponse.status,\n                    provider_error: err\n                };\n                throw p_error;\n            }\n            if (requestPayload.stream) {\n                if (!rawResponse.body) {\n                    p_error = {\n                        message: 'OpenRouter stream body null',\n                        status: 500\n                    };\n                    throw p_error;\n                }\n                p_response = rawResponse;\n                p_responseData = {\n                    note: \"streamed\"\n                };\n            } else {\n                p_responseData = await rawResponse.json();\n            }\n        } else if (providerName?.toLowerCase() === 'google') {\n            // Transform model ID: remove 'models/' prefix for OpenAI endpoint\n            const openAIModelId = effectiveModelId?.replace(/^models\\//, '') || effectiveModelId;\n            const googleApiUrl = `https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`;\n            const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;\n            const googlePayload = {\n                model: openAIModelId,\n                messages: requestPayload.messages,\n                stream: requestPayload.stream || false\n            };\n            // Add optional parameters if they exist\n            if (requestPayload.temperature !== undefined) googlePayload.temperature = requestPayload.temperature;\n            if (requestPayload.max_tokens !== undefined) googlePayload.max_tokens = requestPayload.max_tokens;\n            const googleFetchOptions = {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKeyToUse}`,\n                    'User-Agent': 'RoKey/1.0',\n                    'Origin': 'https://rokey.app',\n                    'Cache-Control': 'no-cache'\n                },\n                body: JSON.stringify(googlePayload)\n            };\n            console.log(`[Google] Attempting connection to Google Gemini OpenAI-compatible API...`);\n            // Check if this is an orchestration context (role parameter indicates orchestration)\n            const isOrchestration = requestPayload.role !== undefined;\n            const rawResponse = await robustFetch(googleApiUrl, googleFetchOptions, 3, undefined, isOrchestration);\n            p_llmResponseTimestamp = new Date();\n            p_status = rawResponse.status;\n            p_responseHeaders = rawResponse.headers;\n            if (!rawResponse.ok) {\n                const err = await rawResponse.json().catch(()=>({\n                        error: {\n                            message: rawResponse.statusText\n                        }\n                    }));\n                let errMsg = err?.error?.message || rawResponse.statusText;\n                if (Array.isArray(err) && err[0]?.error?.message) errMsg = err[0].error.message;\n                p_error = {\n                    message: `Google Error: ${errMsg}`,\n                    status: rawResponse.status,\n                    provider_error: err\n                };\n                throw p_error;\n            }\n            if (requestPayload.stream) {\n                if (!rawResponse.body) {\n                    p_error = {\n                        message: 'Google stream body null',\n                        status: 500\n                    };\n                    throw p_error;\n                }\n                p_response = rawResponse;\n                p_responseData = {\n                    note: \"streamed\"\n                };\n            } else {\n                p_responseData = await rawResponse.json();\n            }\n        } else if (providerName?.toLowerCase() === 'anthropic') {\n            const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;\n            const payload = {\n                ...providerAPIPayload,\n                model: effectiveModelId,\n                messages: requestPayload.messages,\n                stream: requestPayload.stream\n            };\n            Object.keys(payload).forEach((key)=>payload[key] === undefined && delete payload[key]);\n            // Use clean options object (avoid problematic headers)\n            const anthropicOptions = {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKeyToUse}`,\n                    'User-Agent': 'RoKey/1.0',\n                    'Origin': 'https://rokey.app',\n                    'Cache-Control': 'no-cache'\n                },\n                body: JSON.stringify(payload)\n            };\n            console.log(`[Anthropic] Attempting connection to Anthropic OpenAI-compatible API...`);\n            const rawResponse = await robustFetch('https://api.anthropic.com/v1/chat/completions', anthropicOptions);\n            p_llmResponseTimestamp = new Date();\n            p_status = rawResponse.status;\n            p_responseHeaders = rawResponse.headers;\n            if (!rawResponse.ok) {\n                const err = await rawResponse.json().catch(()=>({\n                        error: {\n                            message: rawResponse.statusText\n                        }\n                    }));\n                p_error = {\n                    message: `Anthropic Error: ${err?.error?.message || rawResponse.statusText}`,\n                    status: rawResponse.status,\n                    provider_error: err\n                };\n                throw p_error;\n            }\n            if (requestPayload.stream) {\n                if (!rawResponse.body) {\n                    p_error = {\n                        message: 'Anthropic stream body null',\n                        status: 500\n                    };\n                    throw p_error;\n                }\n                p_response = rawResponse;\n                p_responseData = {\n                    note: \"streamed\"\n                };\n            } else {\n                p_responseData = await rawResponse.json();\n            }\n        } else if (providerName?.toLowerCase() === 'deepseek') {\n            const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;\n            const payload = {\n                ...providerAPIPayload,\n                model: effectiveModelId,\n                messages: requestPayload.messages,\n                stream: requestPayload.stream\n            };\n            Object.keys(payload).forEach((key)=>payload[key] === undefined && delete payload[key]);\n            // Use clean options object (avoid problematic headers)\n            const deepseekOptions = {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKeyToUse}`,\n                    'User-Agent': 'RoKey/1.0',\n                    'Origin': 'https://rokey.app',\n                    'Cache-Control': 'no-cache'\n                },\n                body: JSON.stringify(payload)\n            };\n            console.log(`[DeepSeek] Attempting connection to DeepSeek API...`);\n            const rawResponse = await robustFetch('https://api.deepseek.com/v1/chat/completions', deepseekOptions);\n            p_llmResponseTimestamp = new Date();\n            p_status = rawResponse.status;\n            p_responseHeaders = rawResponse.headers;\n            if (!rawResponse.ok) {\n                const err = await rawResponse.json().catch(()=>({\n                        error: {\n                            message: rawResponse.statusText\n                        }\n                    }));\n                p_error = {\n                    message: `DeepSeek Error: ${err?.error?.message || rawResponse.statusText}`,\n                    status: rawResponse.status,\n                    provider_error: err\n                };\n                throw p_error;\n            }\n            if (requestPayload.stream) {\n                if (!rawResponse.body) {\n                    p_error = {\n                        message: 'DeepSeek stream body null',\n                        status: 500\n                    };\n                    throw p_error;\n                }\n                p_response = rawResponse;\n                p_responseData = {\n                    note: \"streamed\"\n                };\n            } else {\n                p_responseData = await rawResponse.json();\n            }\n        } else if (providerName?.toLowerCase() === 'xai') {\n            const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;\n            const payload = {\n                ...providerAPIPayload,\n                model: effectiveModelId,\n                messages: requestPayload.messages,\n                stream: requestPayload.stream || false\n            };\n            Object.keys(payload).forEach((key)=>payload[key] === undefined && delete payload[key]);\n            // Use clean options object (avoid problematic headers)\n            const xaiOptions = {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${apiKeyToUse}`,\n                    'User-Agent': 'RoKey/1.0',\n                    'Origin': 'https://rokey.app',\n                    'Cache-Control': 'no-cache'\n                },\n                body: JSON.stringify(payload)\n            };\n            console.log(`[XAI] Attempting connection to XAI/Grok API...`);\n            const rawResponse = await robustFetch('https://api.x.ai/v1/chat/completions', xaiOptions);\n            p_llmResponseTimestamp = new Date();\n            p_status = rawResponse.status;\n            p_responseHeaders = rawResponse.headers;\n            if (!rawResponse.ok) {\n                const err = await rawResponse.json().catch(()=>({\n                        error: {\n                            message: rawResponse.statusText\n                        }\n                    }));\n                p_error = {\n                    message: `XAI/Grok Error: ${err?.error?.message || rawResponse.statusText} (Type: ${err?.error?.type})`,\n                    status: rawResponse.status,\n                    provider_error: err\n                };\n                throw p_error;\n            }\n            if (requestPayload.stream) {\n                if (!rawResponse.body) {\n                    p_error = {\n                        message: 'XAI stream body null',\n                        status: 500\n                    };\n                    throw p_error;\n                }\n                p_response = rawResponse;\n                p_responseData = {\n                    note: \"streamed\"\n                };\n            } else {\n                p_responseData = await rawResponse.json();\n            }\n        } else {\n            p_error = {\n                message: `Provider '${providerName}' is configured but not supported by RoKey proxy (executeProviderRequest).`,\n                status: 501,\n                internal: true\n            };\n            throw p_error;\n        }\n        // Cache successful non-streaming responses\n        if (!requestPayload.stream && p_responseData && requestPayload.messages && modelIdInDb) {\n            const cacheKey = generateResponseCacheKey(requestPayload.messages, modelIdInDb, requestPayload.temperature);\n            responseCache.set(cacheKey, {\n                response: p_responseData,\n                timestamp: Date.now(),\n                provider: providerName || 'unknown',\n                model: modelIdInDb\n            });\n            // Cleanup cache if needed\n            cleanupResponseCache();\n            console.log(`[ResponseCache] Cached response for ${providerName}/${modelIdInDb} (cache size: ${responseCache.size})`);\n        }\n        return {\n            success: true,\n            response: p_response,\n            responseData: p_responseData,\n            responseHeaders: p_responseHeaders,\n            status: p_status,\n            error: null,\n            llmRequestTimestamp: p_llmRequestTimestamp,\n            llmResponseTimestamp: p_llmResponseTimestamp\n        };\n    } catch (errorCaught) {\n        // Enhanced error handling with network diagnostics\n        const finalError = p_error || errorCaught;\n        // Add network diagnostic information\n        let errorType = 'ProviderCommsError';\n        let diagnosticInfo = '';\n        if (errorCaught.name === 'AbortError') {\n            errorType = 'TimeoutError';\n            diagnosticInfo = `Request timed out (timeout varies by provider and context)`;\n        } else if (errorCaught.message?.includes('fetch failed')) {\n            errorType = 'NetworkError';\n            diagnosticInfo = 'Network connection failed - check internet connectivity';\n        } else if (errorCaught.code === 'ENOTFOUND') {\n            errorType = 'DNSError';\n            diagnosticInfo = 'DNS resolution failed - check network settings';\n        } else if (errorCaught.code === 'ECONNREFUSED') {\n            errorType = 'ConnectionRefused';\n            diagnosticInfo = 'Connection refused by server';\n        }\n        console.error(`[executeProviderRequest] ${errorType} for provider ${providerName}, model ${modelIdInDb}. Status: ${finalError.status}. Message: ${finalError.message}. Diagnostic: ${diagnosticInfo}`, finalError.provider_error);\n        return {\n            success: false,\n            status: finalError.status || 500,\n            error: finalError.provider_error || {\n                message: `${finalError.message}${diagnosticInfo ? ` (${diagnosticInfo})` : ''}`,\n                type: finalError.internal ? 'RoKeyInternal' : errorType,\n                diagnostic: diagnosticInfo\n            },\n            llmRequestTimestamp: p_llmRequestTimestamp || new Date(),\n            llmResponseTimestamp: p_llmResponseTimestamp || new Date(),\n            response: undefined,\n            responseData: undefined,\n            responseHeaders: p_responseHeaders\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/providers/executeProviderRequest.ts\n");

/***/ })

};
;