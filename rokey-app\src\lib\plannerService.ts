// Planner Service - Generates optimized browsing plans using AI
// Handles the backend planning prompt engineering for Manual Build workflows

import { BrowsingPlan, BrowsingSubtask } from './intelligentBrowsing';

export interface PlannerConfig {
  providerId: string;
  modelId: string;
  apiKey?: string;
  maxSubtasks: number;
  temperature: number;
  maxTokens?: number;
}

export class PlannerService {
  private config: PlannerConfig;

  constructor(config: PlannerConfig) {
    this.config = config;
  }

  // Generate a browsing plan using optimized prompt engineering
  async generateBrowsingPlan(userRequest: string): Promise<BrowsingPlan> {
    const planningPrompt = this.buildPlanningPrompt(userRequest);
    
    try {
      const response = await this.callAIProvider(planningPrompt);
      const plan = this.parsePlanResponse(response);
      
      // Validate and enhance the plan
      return this.validateAndEnhancePlan(plan, userRequest);
    } catch (error) {
      console.error('Planning failed:', error);
      throw new Error(`Failed to generate browsing plan: ${error}`);
    }
  }

  // Build the optimized planning prompt
  private buildPlanningPrompt(userRequest: string): string {
    return `You are an expert web browsing task planner. Your job is to break down complex browsing requests into specific, actionable subtasks that an autonomous browsing agent can execute.

CRITICAL RULES:
1. ALWAYS start with comprehensive search and website gathering (30-40% of subtasks)
2. Use snippet analysis to select best websites instead of visiting everything
3. Be extremely specific with search queries and targets
4. Include CSS selectors for data extraction when possible
5. Plan for early completion detection and data validation
6. Maximum subtasks allowed: ${this.config.maxSubtasks}
7. Estimate realistic timeframes in seconds

AVAILABLE SUBTASK TYPES:
- search: Search engines for information (Google, Bing)
- analyze_snippets: Analyze search results to select best websites
- navigate: Go to specific URLs (only selected ones)
- extract: Extract data from pages using CSS selectors
- screenshot: Capture page screenshots for evidence
- form_fill: Fill out web forms with data
- click: Click specific elements on pages
- wait: Wait for page loads or timed delays
- check_completion: Assess if enough data has been gathered

PLANNING STRATEGY:
Phase 1: DISCOVERY (30-40% of subtasks)
- Multiple search queries with different keywords and angles
- Cast a wide net with comprehensive search terms
- Gather extensive list of potential sources

Phase 2: SMART SELECTION (10-15% of subtasks)
- Analyze search snippets to identify best sources
- Select 3-5 most promising websites based on relevance
- Avoid visiting low-quality or irrelevant sites

Phase 3: TARGETED EXTRACTION (40-50% of subtasks)
- Navigate only to selected high-quality websites
- Extract specific data points with precise selectors
- Take screenshots for verification and evidence
- Check completion status after each major data collection

Phase 4: VALIDATION & SYNTHESIS (5-10% of subtasks)
- Validate data accuracy and completeness
- Cross-reference information between sources
- Final completion check before concluding

EXAMPLE OF EXCELLENT PLANNING:

User Request: "Find the earliest flight from Owerri to Abuja today and then to Dubai"

Good Todo List Structure:
- [x] Search for "Owerri to Abuja flights today" on Google
- [ ] Search for "Owerri Abuja flight schedule May 26 2025" with airline focus
- [ ] Search for "Nigerian domestic flights Owerri Abuja" for comprehensive coverage
- [ ] Search for "Air Peace Arik Air flights Owerri Abuja today" for specific airlines
- [ ] Analyze search snippets to identify best airline websites and booking platforms
- [ ] Navigate to top 3 selected airline websites (Air Peace, Arik Air, etc.)
- [ ] Extract flight times, prices, and availability from each selected airline
- [ ] Take screenshots of earliest flight options for verification
- [ ] Check completion: Do we have sufficient Owerri-Abuja flight data?
- [ ] Search for "Abuja to Dubai flights" with departure after earliest domestic arrival
- [ ] Search for "international flights Abuja Dubai connecting" for better options
- [ ] Analyze snippets to select best international airline websites
- [ ] Navigate to selected international airline sites and booking platforms
- [ ] Extract Dubai flight options that connect properly with timing
- [ ] Take screenshots of best connecting flight options
- [ ] Check completion: Do we have complete travel itinerary?
- [ ] Calculate total travel time including layover periods
- [ ] Validate all flight data for accuracy and current availability
- [ ] Compile comprehensive travel itinerary with all details

ANOTHER EXAMPLE:

User Request: "Compare MacBook Pro prices across 3 retailers"

Good Todo List Structure:
- [x] Search for "MacBook Pro 16-inch M3 price comparison" on Google
- [ ] Search for "MacBook Pro M3 deals discounts" for current promotions
- [ ] Search for "MacBook Pro official price Apple store" for baseline pricing
- [ ] Search for "MacBook Pro Amazon Best Buy price" for major retailers
- [ ] Analyze search snippets to identify best retailer websites with current pricing
- [ ] Navigate to Apple Store MacBook Pro page (official pricing baseline)
- [ ] Extract price, specifications, and availability from Apple
- [ ] Take screenshot of Apple product page for reference
- [ ] Navigate to top-rated Amazon MacBook Pro listings (selected from snippets)
- [ ] Extract prices from best Amazon sellers with good ratings
- [ ] Take screenshot of best Amazon deal found
- [ ] Navigate to Best Buy MacBook Pro page (selected from snippets)
- [ ] Extract Best Buy price, promotions, and stock status
- [ ] Take screenshot of Best Buy listing
- [ ] Check completion: Do we have prices from 3 major retailers?
- [ ] Cross-reference specifications to ensure comparing same model
- [ ] Validate current pricing and stock availability
- [ ] Create comparison table with findings and recommendations

OUTPUT FORMAT REQUIREMENTS:
Return ONLY valid JSON in this exact structure:
{
  "id": "unique-task-id-${Date.now()}",
  "task": "clear description of the overall task",
  "subtasks": [
    {
      "id": "subtask-1",
      "type": "search|analyze_snippets|navigate|extract|screenshot|form_fill|click|wait|check_completion",
      "description": "specific action description",
      "target": "search query, URL, or CSS selector",
      "parameters": {
        "originalTask": "${userRequest}",
        "keywords": ["relevant", "keywords"],
        "priority": "high|medium|low"
      },
      "priority": "high|medium|low"
    }
  ],
  "estimatedTime": 180,
  "priority": "medium"
}

IMPORTANT NOTES:
- These are EXAMPLES to show format - do NOT copy them exactly
- Adapt the planning strategy to the specific user request
- Always prioritize comprehensive discovery before targeted extraction
- Use snippet analysis to avoid wasting time on irrelevant websites
- Include completion checks to enable early termination when sufficient data is gathered
- Be specific with search queries and include relevant keywords
- Plan for potential failures with alternative approaches
- Focus on efficiency: search broadly, select smartly, extract precisely

User Request: ${userRequest}

Generate an optimal browsing plan following the above guidelines:`;
  }

  // Call the AI provider to generate the plan
  private async callAIProvider(prompt: string): Promise<string> {
    // This would integrate with your existing AI provider system
    // For now, return a placeholder - you'll need to implement the actual API call
    throw new Error('AI provider integration needed - implement callAIProvider method');
  }

  // Parse the AI response into a structured plan
  private parsePlanResponse(response: string): BrowsingPlan {
    try {
      const parsed = JSON.parse(response);
      
      // Validate required fields
      if (!parsed.id || !parsed.task || !Array.isArray(parsed.subtasks)) {
        throw new Error('Invalid plan structure');
      }
      
      return parsed as BrowsingPlan;
    } catch (error) {
      throw new Error(`Failed to parse plan response: ${error}`);
    }
  }

  // Validate and enhance the generated plan
  private validateAndEnhancePlan(plan: BrowsingPlan, originalRequest: string): BrowsingPlan {
    // Ensure we don't exceed max subtasks
    if (plan.subtasks.length > this.config.maxSubtasks) {
      plan.subtasks = plan.subtasks.slice(0, this.config.maxSubtasks);
    }

    // Ensure first subtask is always a search
    if (plan.subtasks.length > 0 && plan.subtasks[0].type !== 'search') {
      plan.subtasks.unshift({
        id: 'initial-search',
        type: 'search',
        description: `Search for information about: ${originalRequest}`,
        target: originalRequest,
        parameters: {
          originalTask: originalRequest,
          keywords: this.extractKeywords(originalRequest),
          priority: 'high'
        },
        priority: 'high',
        status: 'pending'
      });
    }

    // Add completion checks if missing
    const hasCompletionCheck = plan.subtasks.some(st => st.type === 'check_completion');
    if (!hasCompletionCheck && plan.subtasks.length > 3) {
      const midPoint = Math.floor(plan.subtasks.length / 2);
      plan.subtasks.splice(midPoint, 0, {
        id: 'mid-completion-check',
        type: 'check_completion',
        description: 'Check if sufficient data has been gathered',
        parameters: {
          originalTask: originalRequest,
          priority: 'medium'
        },
        priority: 'medium',
        status: 'pending'
      });
    }

    return plan;
  }

  // Extract keywords from user request
  private extractKeywords(request: string): string[] {
    // Simple keyword extraction - can be enhanced with NLP
    const words = request.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['this', 'that', 'with', 'from', 'they', 'have', 'been', 'will', 'would', 'could', 'should'].includes(word));
    
    return [...new Set(words)].slice(0, 10); // Unique keywords, max 10
  }
}

export default PlannerService;
