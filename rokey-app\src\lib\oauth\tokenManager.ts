// OAuth Token Management
// Handles secure storage, retrieval, and refresh of OAuth tokens

import { createServiceRoleClient } from '@/lib/supabase/server';
import { getOAuthConfigForTool } from './config';
import crypto from 'crypto';

export interface OAuthTokenData {
  id: string;
  user_id: string;
  tool_type: string;
  access_token: string;
  refresh_token?: string;
  expires_at?: Date;
  scopes: string[];
  provider_user_id?: string;
  provider_user_email?: string;
  provider_user_name?: string;
  connection_status: 'connected' | 'expired' | 'revoked' | 'error';
  last_used_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface TokenRefreshResult {
  success: boolean;
  access_token?: string;
  refresh_token?: string;
  expires_at?: Date;
  error?: string;
}

// Encryption utilities
const ENCRYPTION_KEY = process.env.OAUTH_ENCRYPTION_KEY || process.env.ROKEY_ENCRYPTION_KEY || '';
const ALGORITHM = 'aes-256-gcm';

function encrypt(text: string): string {
  if (!ENCRYPTION_KEY) {
    throw new Error('Encryption key not configured');
  }

  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);

  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const authTag = cipher.getAuthTag();

  return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
}

function decrypt(encryptedText: string): string {
  if (!ENCRYPTION_KEY) {
    throw new Error('Encryption key not configured');
  }

  const parts = encryptedText.split(':');
  if (parts.length !== 3) {
    throw new Error('Invalid encrypted data format');
  }

  const iv = Buffer.from(parts[0], 'hex');
  const authTag = Buffer.from(parts[1], 'hex');
  const encrypted = parts[2];

  const decipher = crypto.createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY.slice(0, 32)), iv);
  decipher.setAuthTag(authTag);

  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

// Store OAuth tokens securely
export async function storeOAuthTokens(
  userId: string,
  toolType: string,
  tokenData: {
    access_token: string;
    refresh_token?: string;
    expires_in?: number;
    scope?: string;
    provider_user_id?: string;
    provider_user_email?: string;
    provider_user_name?: string;
  }
): Promise<OAuthTokenData | null> {
  try {
    const supabase = createServiceRoleClient();
    
    // Calculate expiration time
    const expiresAt = tokenData.expires_in 
      ? new Date(Date.now() + tokenData.expires_in * 1000)
      : null;
    
    // Parse scopes
    const scopes = tokenData.scope ? tokenData.scope.split(' ') : [];
    
    // Encrypt sensitive data
    const encryptedAccessToken = encrypt(tokenData.access_token);
    const encryptedRefreshToken = tokenData.refresh_token ? encrypt(tokenData.refresh_token) : null;
    
    const { data, error } = await supabase
      .from('tool_oauth_connections')
      .upsert({
        user_id: userId,
        tool_type: toolType,
        access_token: encryptedAccessToken,
        refresh_token: encryptedRefreshToken,
        expires_at: expiresAt?.toISOString(),
        scopes,
        provider_user_id: tokenData.provider_user_id,
        provider_user_email: tokenData.provider_user_email,
        provider_user_name: tokenData.provider_user_name,
        connection_status: 'connected',
        last_used_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,tool_type'
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error storing OAuth tokens:', error);
      return null;
    }
    
    return {
      ...data,
      access_token: tokenData.access_token, // Return decrypted for immediate use
      refresh_token: tokenData.refresh_token,
      expires_at: expiresAt,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at)
    };
  } catch (error) {
    console.error('Error in storeOAuthTokens:', error);
    return null;
  }
}

// Retrieve OAuth tokens for a user and tool
export async function getOAuthTokens(
  userId: string,
  toolType: string
): Promise<OAuthTokenData | null> {
  try {
    const supabase = createServiceRoleClient();
    
    const { data, error } = await supabase
      .from('tool_oauth_connections')
      .select('*')
      .eq('user_id', userId)
      .eq('tool_type', toolType)
      .eq('connection_status', 'connected')
      .single();
    
    if (error || !data) {
      return null;
    }
    
    // Decrypt sensitive data
    const accessToken = decrypt(data.access_token);
    const refreshToken = data.refresh_token ? decrypt(data.refresh_token) : undefined;
    
    return {
      ...data,
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_at: data.expires_at ? new Date(data.expires_at) : undefined,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at)
    };
  } catch (error) {
    console.error('Error retrieving OAuth tokens:', error);
    return null;
  }
}

// Check if token needs refresh
export function needsTokenRefresh(tokenData: OAuthTokenData): boolean {
  if (!tokenData.expires_at) {
    return false; // No expiration info, assume it's valid
  }
  
  // Refresh if token expires within 5 minutes
  const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
  return tokenData.expires_at <= fiveMinutesFromNow;
}

// Refresh OAuth token
export async function refreshOAuthToken(
  userId: string,
  toolType: string
): Promise<TokenRefreshResult> {
  try {
    const tokenData = await getOAuthTokens(userId, toolType);
    if (!tokenData || !tokenData.refresh_token) {
      return { success: false, error: 'No refresh token available' };
    }
    
    const config = getOAuthConfigForTool(toolType);
    if (!config) {
      return { success: false, error: 'OAuth configuration not found' };
    }
    
    // Prepare refresh request
    const refreshData = new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: tokenData.refresh_token,
      client_id: config.clientId,
      client_secret: config.clientSecret
    });
    
    const response = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: refreshData
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Token refresh failed:', errorText);
      return { success: false, error: 'Token refresh failed' };
    }
    
    const refreshResult = await response.json();
    
    // Store updated tokens
    const updatedTokenData = await storeOAuthTokens(userId, toolType, {
      access_token: refreshResult.access_token,
      refresh_token: refreshResult.refresh_token || tokenData.refresh_token,
      expires_in: refreshResult.expires_in,
      scope: refreshResult.scope || tokenData.scopes.join(' '),
      provider_user_id: tokenData.provider_user_id,
      provider_user_email: tokenData.provider_user_email,
      provider_user_name: tokenData.provider_user_name
    });
    
    if (!updatedTokenData) {
      return { success: false, error: 'Failed to store refreshed tokens' };
    }
    
    return {
      success: true,
      access_token: refreshResult.access_token,
      refresh_token: refreshResult.refresh_token || tokenData.refresh_token,
      expires_at: updatedTokenData.expires_at
    };
  } catch (error) {
    console.error('Error refreshing OAuth token:', error);
    return { success: false, error: 'Token refresh error' };
  }
}

// Get valid access token (with automatic refresh)
export async function getValidAccessToken(
  userId: string,
  toolType: string
): Promise<string | null> {
  try {
    let tokenData = await getOAuthTokens(userId, toolType);
    if (!tokenData) {
      return null;
    }
    
    // Check if token needs refresh
    if (needsTokenRefresh(tokenData)) {
      const refreshResult = await refreshOAuthToken(userId, toolType);
      if (refreshResult.success && refreshResult.access_token) {
        return refreshResult.access_token;
      } else {
        // Mark token as expired
        await updateConnectionStatus(userId, toolType, 'expired');
        return null;
      }
    }
    
    // Update last used timestamp
    await updateLastUsed(userId, toolType);
    
    return tokenData.access_token;
  } catch (error) {
    console.error('Error getting valid access token:', error);
    return null;
  }
}

// Update connection status
export async function updateConnectionStatus(
  userId: string,
  toolType: string,
  status: 'connected' | 'expired' | 'revoked' | 'error'
): Promise<boolean> {
  try {
    const supabase = createServiceRoleClient();
    
    const { error } = await supabase
      .from('tool_oauth_connections')
      .update({ connection_status: status })
      .eq('user_id', userId)
      .eq('tool_type', toolType);
    
    return !error;
  } catch (error) {
    console.error('Error updating connection status:', error);
    return false;
  }
}

// Update last used timestamp
export async function updateLastUsed(
  userId: string,
  toolType: string
): Promise<boolean> {
  try {
    const supabase = createServiceRoleClient();
    
    const { error } = await supabase
      .from('tool_oauth_connections')
      .update({ last_used_at: new Date().toISOString() })
      .eq('user_id', userId)
      .eq('tool_type', toolType);
    
    return !error;
  } catch (error) {
    console.error('Error updating last used timestamp:', error);
    return false;
  }
}

// Revoke OAuth connection
export async function revokeOAuthConnection(
  userId: string,
  toolType: string
): Promise<boolean> {
  try {
    const supabase = createServiceRoleClient();
    
    const { error } = await supabase
      .from('tool_oauth_connections')
      .delete()
      .eq('user_id', userId)
      .eq('tool_type', toolType);
    
    return !error;
  } catch (error) {
    console.error('Error revoking OAuth connection:', error);
    return false;
  }
}

// Get all tool connections for a user
export async function getUserToolConnections(
  userId: string
): Promise<OAuthTokenData[]> {
  try {
    const supabase = createServiceRoleClient();
    
    const { data, error } = await supabase
      .from('tool_oauth_connections')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error || !data) {
      return [];
    }
    
    return data.map(item => ({
      ...item,
      access_token: '', // Don't return actual tokens in list view
      refresh_token: undefined,
      expires_at: item.expires_at ? new Date(item.expires_at) : undefined,
      created_at: new Date(item.created_at),
      updated_at: new Date(item.updated_at)
    }));
  } catch (error) {
    console.error('Error getting user tool connections:', error);
    return [];
  }
}
