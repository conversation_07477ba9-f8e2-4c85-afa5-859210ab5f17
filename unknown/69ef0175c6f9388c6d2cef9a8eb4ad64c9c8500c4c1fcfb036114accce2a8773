/**
 * Workflow Sharing Service for Manual Build
 * Handles workflow sharing, permissions, export/import, and collaboration
 */

import { createClient } from '@supabase/supabase-js';
import { ManualBuildWorkflow } from '@/types/manualBuild';

export interface WorkflowShare {
  id: string;
  workflow_id: string;
  shared_by: string;
  shared_with?: string; // null for public shares
  permission_level: 'view' | 'edit' | 'admin';
  share_token: string;
  expires_at?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface WorkflowExport {
  workflow: ManualBuildWorkflow;
  metadata: {
    exportedAt: string;
    exportedBy: string;
    version: string;
    dependencies?: string[];
  };
  format: 'json' | 'yaml';
}

export interface SharePermissions {
  canView: boolean;
  canEdit: boolean;
  canShare: boolean;
  canDelete: boolean;
  canExport: boolean;
}

export class WorkflowSharingService {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  /**
   * Share a workflow with specific users or make it public
   */
  async shareWorkflow(
    workflowId: string,
    sharedBy: string,
    options: {
      sharedWith?: string[];
      permissionLevel: 'view' | 'edit' | 'admin';
      isPublic?: boolean;
      expiresAt?: Date;
    }
  ): Promise<WorkflowShare[]> {
    const { sharedWith, permissionLevel, isPublic = false, expiresAt } = options;

    // Verify the user owns the workflow
    const { data: workflow, error: workflowError } = await this.supabase
      .from('manual_build_workflows')
      .select('user_id')
      .eq('id', workflowId)
      .eq('user_id', sharedBy)
      .single();

    if (workflowError || !workflow) {
      throw new Error('Workflow not found or access denied');
    }

    const shares: WorkflowShare[] = [];

    if (isPublic) {
      // Create public share
      const shareToken = this.generateShareToken();
      const { data: publicShare, error } = await this.supabase
        .from('workflow_shares')
        .insert({
          workflow_id: workflowId,
          shared_by: sharedBy,
          shared_with: null,
          permission_level: permissionLevel,
          share_token: shareToken,
          expires_at: expiresAt?.toISOString(),
          is_public: true
        })
        .select()
        .single();

      if (error) throw error;
      shares.push(publicShare);
    }

    if (sharedWith && sharedWith.length > 0) {
      // Create individual shares
      const shareInserts = sharedWith.map(userId => ({
        workflow_id: workflowId,
        shared_by: sharedBy,
        shared_with: userId,
        permission_level: permissionLevel,
        share_token: this.generateShareToken(),
        expires_at: expiresAt?.toISOString(),
        is_public: false
      }));

      const { data: individualShares, error } = await this.supabase
        .from('workflow_shares')
        .insert(shareInserts)
        .select();

      if (error) throw error;
      shares.push(...individualShares);
    }

    return shares;
  }

  /**
   * Get workflow by share token
   */
  async getSharedWorkflow(shareToken: string, userId?: string): Promise<{
    workflow: ManualBuildWorkflow;
    permissions: SharePermissions;
    share: WorkflowShare;
  }> {
    // Get share information
    const { data: share, error: shareError } = await this.supabase
      .from('workflow_shares')
      .select('*')
      .eq('share_token', shareToken)
      .single();

    if (shareError || !share) {
      throw new Error('Invalid share token');
    }

    // Check if share has expired
    if (share.expires_at && new Date(share.expires_at) < new Date()) {
      throw new Error('Share link has expired');
    }

    // Check permissions
    if (!share.is_public && share.shared_with !== userId) {
      throw new Error('Access denied');
    }

    // Get workflow
    const { data: workflow, error: workflowError } = await this.supabase
      .from('manual_build_workflows')
      .select('*')
      .eq('id', share.workflow_id)
      .single();

    if (workflowError || !workflow) {
      throw new Error('Workflow not found');
    }

    // Calculate permissions
    const permissions = this.calculatePermissions(share, userId);

    return { workflow, permissions, share };
  }

  /**
   * Export workflow to various formats
   */
  async exportWorkflow(
    workflowId: string,
    userId: string,
    format: 'json' | 'yaml' = 'json'
  ): Promise<WorkflowExport> {
    // Verify access
    const { data: workflow, error } = await this.supabase
      .from('manual_build_workflows')
      .select('*')
      .eq('id', workflowId)
      .eq('user_id', userId)
      .single();

    if (error || !workflow) {
      throw new Error('Workflow not found or access denied');
    }

    const exportData: WorkflowExport = {
      workflow,
      metadata: {
        exportedAt: new Date().toISOString(),
        exportedBy: userId,
        version: '1.0.0',
        dependencies: this.extractDependencies(workflow)
      },
      format
    };

    return exportData;
  }

  /**
   * Import workflow from export data
   */
  async importWorkflow(
    exportData: WorkflowExport,
    userId: string,
    options: {
      name?: string;
      preserveIds?: boolean;
    } = {}
  ): Promise<ManualBuildWorkflow> {
    const { name, preserveIds = false } = options;
    const { workflow } = exportData;

    // Prepare workflow for import
    const importedWorkflow = {
      ...workflow,
      id: preserveIds ? workflow.id : undefined, // Let DB generate new ID
      user_id: userId,
      name: name || `${workflow.name} (Imported)`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Reset sharing-related fields
      is_template: false,
      template_category: null
    };

    // If not preserving IDs, regenerate node and edge IDs
    if (!preserveIds) {
      const idMap = new Map<string, string>();
      
      // Generate new node IDs
      importedWorkflow.nodes = workflow.nodes.map(node => {
        const newId = `${node.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        idMap.set(node.id, newId);
        return { ...node, id: newId };
      });

      // Update edge references
      importedWorkflow.edges = workflow.edges.map(edge => ({
        ...edge,
        id: `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        source: idMap.get(edge.source) || edge.source,
        target: idMap.get(edge.target) || edge.target
      }));
    }

    // Insert into database
    const { data: newWorkflow, error } = await this.supabase
      .from('manual_build_workflows')
      .insert(importedWorkflow)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to import workflow: ${error.message}`);
    }

    return newWorkflow;
  }

  /**
   * Get all shares for a workflow
   */
  async getWorkflowShares(workflowId: string, userId: string): Promise<WorkflowShare[]> {
    const { data: shares, error } = await this.supabase
      .from('workflow_shares')
      .select('*')
      .eq('workflow_id', workflowId)
      .eq('shared_by', userId);

    if (error) throw error;
    return shares || [];
  }

  /**
   * Revoke a share
   */
  async revokeShare(shareId: string, userId: string): Promise<void> {
    const { error } = await this.supabase
      .from('workflow_shares')
      .delete()
      .eq('id', shareId)
      .eq('shared_by', userId);

    if (error) throw error;
  }

  /**
   * Update share permissions
   */
  async updateSharePermissions(
    shareId: string,
    userId: string,
    permissionLevel: 'view' | 'edit' | 'admin'
  ): Promise<WorkflowShare> {
    const { data: share, error } = await this.supabase
      .from('workflow_shares')
      .update({ permission_level: permissionLevel })
      .eq('id', shareId)
      .eq('shared_by', userId)
      .select()
      .single();

    if (error) throw error;
    return share;
  }

  /**
   * Generate a secure share token
   */
  private generateShareToken(): string {
    return `wf_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }

  /**
   * Calculate permissions based on share and user
   */
  private calculatePermissions(share: WorkflowShare, userId?: string): SharePermissions {
    const isOwner = userId === share.shared_by;
    const hasAccess = share.is_public || share.shared_with === userId || isOwner;

    if (!hasAccess) {
      return {
        canView: false,
        canEdit: false,
        canShare: false,
        canDelete: false,
        canExport: false
      };
    }

    switch (share.permission_level) {
      case 'admin':
        return {
          canView: true,
          canEdit: true,
          canShare: true,
          canDelete: isOwner,
          canExport: true
        };
      case 'edit':
        return {
          canView: true,
          canEdit: true,
          canShare: false,
          canDelete: false,
          canExport: true
        };
      case 'view':
      default:
        return {
          canView: true,
          canEdit: false,
          canShare: false,
          canDelete: false,
          canExport: false
        };
    }
  }

  /**
   * Extract dependencies from workflow
   */
  private extractDependencies(workflow: ManualBuildWorkflow): string[] {
    const dependencies = new Set<string>();

    workflow.nodes.forEach(node => {
      // Add node type as dependency
      dependencies.add(`node:${node.type}`);

      // Add provider dependencies
      if (node.type === 'provider' || node.type === 'vision') {
        const config = node.data.config as any;
        if (config?.providerId) {
          dependencies.add(`provider:${config.providerId}`);
        }
      }

      // Add tool dependencies
      if (node.type === 'tool') {
        const config = node.data.config as any;
        if (config?.toolType) {
          dependencies.add(`tool:${config.toolType}`);
        }
      }
    });

    return Array.from(dependencies);
  }
}

// Export singleton instance
export const workflowSharingService = new WorkflowSharingService();

// Add workflow_shares table structure for reference
export interface WorkflowSharesTable {
  id: string;
  workflow_id: string;
  shared_by: string;
  shared_with?: string;
  permission_level: 'view' | 'edit' | 'admin';
  share_token: string;
  expires_at?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}
