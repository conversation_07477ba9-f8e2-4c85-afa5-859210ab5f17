// Additional Tool Implementation Methods
// Contains the remaining API implementations for tools

import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedPatch } from '@/lib/oauth/middleware';

// Google Calendar API implementations
export class GoogleCalendarAPI {
  static async createEvent(userId: string, params: any, timeout: number) {
    const { summary, start, end, description, attendees, calendarId = 'primary' } = params;
    if (!summary) throw new Error('Event summary is required');
    if (!start) throw new Error('Event start time is required');
    if (!end) throw new Error('Event end time is required');

    const url = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events`;
    const body = {
      summary,
      start: { dateTime: start },
      end: { dateTime: end },
      ...(description && { description }),
      ...(attendees && { attendees: attendees.map((email: string) => ({ email })) })
    };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'calendar',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      event: data
    };
  }

  static async listEvents(userId: string, params: any, timeout: number) {
    const { calendarId = 'primary', timeMin, timeMax, maxResults = 10 } = params;

    let url = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events?maxResults=${maxResults}&singleEvents=true&orderBy=startTime`;
    
    if (timeMin) url += `&timeMin=${encodeURIComponent(timeMin)}`;
    if (timeMax) url += `&timeMax=${encodeURIComponent(timeMax)}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'calendar',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      events: data.items || []
    };
  }

  static async getEvent(userId: string, params: any, timeout: number) {
    const { eventId, calendarId = 'primary' } = params;
    if (!eventId) throw new Error('Event ID is required');

    const url = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events/${eventId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'calendar',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      event: data
    };
  }

  static async updateEvent(userId: string, params: any, timeout: number) {
    const { eventId, calendarId = 'primary', ...updateData } = params;
    if (!eventId) throw new Error('Event ID is required');

    const url = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events/${eventId}`;

    const response = await authenticatedPut(url, updateData, {
      userId,
      toolType: 'calendar',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      event: data
    };
  }

  static async deleteEvent(userId: string, params: any, timeout: number) {
    const { eventId, calendarId = 'primary' } = params;
    if (!eventId) throw new Error('Event ID is required');

    const url = `https://www.googleapis.com/calendar/v3/calendars/${calendarId}/events/${eventId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'calendar',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.status}`);
    }

    return {
      success: true,
      deleted: true,
      eventId
    };
  }
}

// YouTube API implementations
export class YouTubeAPI {
  static async searchVideos(userId: string, params: any, timeout: number) {
    const { query, maxResults = 10, order = 'relevance' } = params;
    if (!query) throw new Error('Search query is required');

    const url = `https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${encodeURIComponent(query)}&maxResults=${maxResults}&order=${order}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'youtube',
      timeout
    });

    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      videos: data.items || [],
      query,
      totalResults: data.pageInfo?.totalResults
    };
  }

  static async getVideo(userId: string, params: any, timeout: number) {
    const { videoId } = params;
    if (!videoId) throw new Error('Video ID is required');

    const url = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=${videoId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'youtube',
      timeout
    });

    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      video: data.items?.[0] || null
    };
  }

  static async getChannel(userId: string, params: any, timeout: number) {
    const { channelId } = params;
    if (!channelId) throw new Error('Channel ID is required');

    const url = `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics,contentDetails&id=${channelId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'youtube',
      timeout
    });

    if (!response.ok) {
      throw new Error(`YouTube API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      channel: data.items?.[0] || null
    };
  }

  static async getAnalytics(userId: string, params: any, timeout: number) {
    const { channelId, startDate, endDate, metrics = 'views,estimatedMinutesWatched,subscribersGained' } = params;
    if (!channelId) throw new Error('Channel ID is required');
    if (!startDate) throw new Error('Start date is required');
    if (!endDate) throw new Error('End date is required');

    const url = `https://youtubeanalytics.googleapis.com/v2/reports?ids=channel==${channelId}&startDate=${startDate}&endDate=${endDate}&metrics=${metrics}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'youtube',
      timeout
    });

    if (!response.ok) {
      throw new Error(`YouTube Analytics API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      analytics: data
    };
  }
}

// Notion API implementations
export class NotionAPI {
  static async createPage(userId: string, params: any, timeout: number) {
    const { parent, properties, children } = params;
    if (!parent) throw new Error('Parent page/database is required');
    if (!properties) throw new Error('Page properties are required');

    const url = 'https://api.notion.com/v1/pages';
    const body = {
      parent,
      properties,
      ...(children && { children })
    };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'notion',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Notion API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      page: data
    };
  }

  static async getPage(userId: string, params: any, timeout: number) {
    const { pageId } = params;
    if (!pageId) throw new Error('Page ID is required');

    const url = `https://api.notion.com/v1/pages/${pageId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'notion',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Notion API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      page: data
    };
  }

  static async updatePage(userId: string, params: any, timeout: number) {
    const { pageId, properties } = params;
    if (!pageId) throw new Error('Page ID is required');
    if (!properties) throw new Error('Properties to update are required');

    const url = `https://api.notion.com/v1/pages/${pageId}`;
    const body = { properties };

    const response = await authenticatedPatch(url, body, {
      userId,
      toolType: 'notion',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Notion API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      page: data
    };
  }

  static async queryDatabase(userId: string, params: any, timeout: number) {
    const { databaseId, filter, sorts, pageSize = 10 } = params;
    if (!databaseId) throw new Error('Database ID is required');

    const url = `https://api.notion.com/v1/databases/${databaseId}/query`;
    const body = {
      page_size: pageSize,
      ...(filter && { filter }),
      ...(sorts && { sorts })
    };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'notion',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Notion API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      results: data.results || [],
      hasMore: data.has_more
    };
  }

  static async createDatabaseEntry(userId: string, params: any, timeout: number) {
    const { databaseId, properties } = params;
    if (!databaseId) throw new Error('Database ID is required');
    if (!properties) throw new Error('Entry properties are required');

    const url = 'https://api.notion.com/v1/pages';
    const body = {
      parent: { database_id: databaseId },
      properties
    };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'notion',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Notion API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      entry: data
    };
  }
}
