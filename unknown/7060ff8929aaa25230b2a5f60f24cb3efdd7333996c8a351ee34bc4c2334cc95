'use client';

import { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  DocumentCheckIcon, 
  KeyIcon,
  ClipboardDocumentIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface WorkflowSaveModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'save' | 'success' | 'error';
  apiKey?: string;
  workflowName?: string;
  errorMessage?: string;
  onSave?: (name: string, description: string) => void;
  isSaving?: boolean;
}

export default function WorkflowSaveModal({
  isOpen,
  onClose,
  mode,
  apiKey,
  workflowName,
  errorMessage,
  onSave,
  isSaving = false
}: WorkflowSaveModalProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [copied, setCopied] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen && mode === 'save') {
      setName('');
      setDescription('');
    }
  }, [isOpen, mode]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen && !isSaving) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, isSaving, onClose]);

  const handleCopyApiKey = async () => {
    if (apiKey) {
      try {
        await navigator.clipboard.writeText(apiKey);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy API key:', error);
      }
    }
  };

  const handleSave = () => {
    if (name.trim() && onSave) {
      onSave(name.trim(), description.trim());
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-xl max-w-md w-full border border-gray-700/50 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div className="flex items-center gap-3">
            {mode === 'save' && <DocumentCheckIcon className="w-6 h-6 text-[#ff6b35]" />}
            {mode === 'success' && <KeyIcon className="w-6 h-6 text-green-500" />}
            {mode === 'error' && <ExclamationTriangleIcon className="w-6 h-6 text-red-500" />}
            <h2 className="text-xl font-bold text-white">
              {mode === 'save' && 'Save Workflow'}
              {mode === 'success' && 'Workflow Saved Successfully!'}
              {mode === 'error' && 'Save Failed'}
            </h2>
          </div>
          {!isSaving && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {mode === 'save' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Workflow Name *
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter workflow name"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
                  autoFocus
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Description (optional)
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter workflow description"
                  rows={3}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35] resize-none"
                />
              </div>
            </div>
          )}

          {mode === 'success' && apiKey && (
            <div className="space-y-4">
              <p className="text-gray-300">
                Your workflow <span className="font-semibold text-white">{workflowName}</span> has been saved successfully!
              </p>
              
              <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700/50">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-300">
                    API Key (save this - it won't be shown again!)
                  </label>
                  <button
                    onClick={handleCopyApiKey}
                    className="flex items-center gap-1 text-xs text-[#ff6b35] hover:text-[#e55a2b] transition-colors"
                  >
                    {copied ? (
                      <>
                        <CheckIcon className="w-3 h-3" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <ClipboardDocumentIcon className="w-3 h-3" />
                        Copy
                      </>
                    )}
                  </button>
                </div>
                <div className="bg-gray-800 rounded px-3 py-2 font-mono text-sm text-green-400 break-all">
                  {apiKey}
                </div>
              </div>

              <div className="bg-amber-900/20 border border-amber-700/50 rounded-lg p-3">
                <p className="text-amber-200 text-sm">
                  <strong>Important:</strong> This API key will not be displayed again. Save it securely to use your workflow via API.
                </p>
              </div>
            </div>
          )}

          {mode === 'error' && (
            <div className="space-y-4">
              <p className="text-gray-300">
                Failed to save the workflow. Please try again.
              </p>
              {errorMessage && (
                <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-3">
                  <p className="text-red-200 text-sm">{errorMessage}</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-700/50">
          {mode === 'save' && (
            <>
              <button
                onClick={onClose}
                disabled={isSaving}
                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 disabled:opacity-50 text-white rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={!name.trim() || isSaving}
                className="flex-1 px-4 py-2 bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Saving...
                  </>
                ) : (
                  'Save Workflow'
                )}
              </button>
            </>
          )}

          {(mode === 'success' || mode === 'error') && (
            <button
              onClick={onClose}
              className="w-full px-4 py-2 bg-[#ff6b35] hover:bg-[#e55a2b] text-white rounded-lg transition-colors"
            >
              {mode === 'success' ? 'Continue' : 'Try Again'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
