/**
 * API endpoints for accessing shared workflows
 * Handles viewing, copying, and exporting shared workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowSharingService } from '@/lib/workflow/WorkflowSharingService';

interface RouteParams {
  params: Promise<{
    shareToken: string;
  }>;
}

/**
 * GET /api/manual-build/shared/[shareToken]
 * Get shared workflow by token
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { shareToken } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Get user (optional for public shares)
    const { data: { user } } = await supabase.auth.getUser();
    
    // Get shared workflow
    const { workflow, permissions, share } = await workflowSharingService.getSharedWorkflow(
      shareToken,
      user?.id
    );

    return NextResponse.json({
      workflow,
      permissions,
      share
    });

  } catch (error) {
    console.error('Get shared workflow error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: error instanceof Error && error.message.includes('Invalid') ? 404 : 500 }
    );
  }
}

/**
 * POST /api/manual-build/shared/[shareToken]/copy
 * Copy shared workflow to user's account
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  const { shareToken } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name } = body;

    // Get shared workflow
    const { workflow, permissions } = await workflowSharingService.getSharedWorkflow(
      shareToken,
      user.id
    );

    // Check export permission
    if (!permissions.canExport) {
      return NextResponse.json(
        { error: 'Export not allowed' },
        { status: 403 }
      );
    }

    // Export and import workflow
    const exportData = await workflowSharingService.exportWorkflow(
      workflow.id,
      workflow.user_id,
      'json'
    );

    const copiedWorkflow = await workflowSharingService.importWorkflow(
      exportData,
      user.id,
      { name, preserveIds: false }
    );

    return NextResponse.json({
      workflow: copiedWorkflow,
      message: 'Workflow copied successfully'
    });

  } catch (error) {
    console.error('Copy shared workflow error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}


