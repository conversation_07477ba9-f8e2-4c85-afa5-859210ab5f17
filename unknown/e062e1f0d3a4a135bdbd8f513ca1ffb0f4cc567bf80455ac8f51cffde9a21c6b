import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { memoryService } from '@/lib/memory/MemoryService';
import { routerMemoryService } from '@/lib/memory/RouterMemoryService';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, memoryName, nodeId, workflowId, data, dataType, config } = body;

    switch (action) {
      case 'store':
        if (!memoryName || !nodeId || !workflowId || !data || !config) {
          return NextResponse.json({ 
            error: 'Missing required fields for store: memoryName, nodeId, workflowId, data, config' 
          }, { status: 400 });
        }

        const storeSuccess = await memoryService.store(
          memoryName,
          nodeId,
          workflowId,
          user.id,
          data,
          dataType || 'general',
          config
        );

        return NextResponse.json({
          success: storeSuccess,
          action: 'store',
          memoryName,
          timestamp: new Date().toISOString()
        });

      case 'retrieve':
        if (!memoryName || !nodeId || !workflowId) {
          return NextResponse.json({ 
            error: 'Missing required fields for retrieve: memoryName, nodeId, workflowId' 
          }, { status: 400 });
        }

        const retrievedData = await memoryService.retrieve(
          memoryName,
          nodeId,
          workflowId,
          user.id
        );

        return NextResponse.json({
          success: retrievedData !== null,
          data: retrievedData,
          action: 'retrieve',
          memoryName,
          timestamp: new Date().toISOString()
        });

      case 'clear':
        if (!nodeId || !workflowId) {
          return NextResponse.json({ 
            error: 'Missing required fields for clear: nodeId, workflowId' 
          }, { status: 400 });
        }

        const clearSuccess = await memoryService.clearNodeMemory(
          nodeId,
          workflowId,
          user.id
        );

        return NextResponse.json({
          success: clearSuccess,
          action: 'clear',
          nodeId,
          timestamp: new Date().toISOString()
        });

      case 'list':
        if (!workflowId) {
          return NextResponse.json({ 
            error: 'Missing required field for list: workflowId' 
          }, { status: 400 });
        }

        const memoryEntries = await memoryService.getWorkflowMemory(workflowId, user.id);

        return NextResponse.json({
          success: true,
          memories: memoryEntries,
          count: memoryEntries.length,
          action: 'list',
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({ 
          error: 'Invalid action. Supported actions: store, retrieve, clear, list' 
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Memory operation error:', error);
    
    return NextResponse.json({
      error: 'Memory operation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflowId');
    const statsType = searchParams.get('stats');

    if (statsType === 'routing') {
      // Get routing memory statistics
      const routingStats = routerMemoryService.getRoutingStats();
      
      return NextResponse.json({
        type: 'routing_stats',
        stats: routingStats,
        timestamp: new Date().toISOString()
      });
    }

    if (statsType === 'memory') {
      // Get general memory statistics
      const memoryStats = memoryService.getStats();
      
      return NextResponse.json({
        type: 'memory_stats',
        stats: memoryStats,
        timestamp: new Date().toISOString()
      });
    }

    if (workflowId) {
      // Get memory entries for a specific workflow
      const memoryEntries = await memoryService.getWorkflowMemory(workflowId, user.id);
      
      return NextResponse.json({
        workflowId,
        memories: memoryEntries,
        count: memoryEntries.length,
        timestamp: new Date().toISOString()
      });
    }

    // Default: return memory service status
    return NextResponse.json({
      status: 'active',
      message: 'Memory service is operational',
      endpoints: {
        'POST /api/manual-build/memory': 'Store, retrieve, clear, or list memory',
        'GET /api/manual-build/memory?workflowId=X': 'Get memory for workflow',
        'GET /api/manual-build/memory?stats=memory': 'Get memory statistics',
        'GET /api/manual-build/memory?stats=routing': 'Get routing statistics'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Memory GET error:', error);
    
    return NextResponse.json({
      error: 'Failed to get memory information',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
