// Workflow Tool Executor
// This handles execution of tools within Manual Build workflows

import { ToolNodeData } from '@/types/manualBuild';
import {
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  authenticatedPatch,
  validateToolConnection
} from '@/lib/oauth/middleware';
import {
  GoogleDriveAP<PERSON>,
  GoogleDocsAPI,
  GoogleSheetsAPI,
  GmailAPI
} from './toolImplementations';
import {
  GoogleCalendarAPI,
  YouTubeAPI,
  NotionAPI
} from './toolImplementations2';

export interface ToolExecutionContext {
  userInput: string;
  previousResults?: any[];
  workflowId: string;
  nodeId: string;
  userId: string;
}

export interface ToolExecutionResult {
  success: boolean;
  data: any;
  toolType: string;
  executionTime: number;
  error?: string;
}

export class ToolExecutor {
  static async executeToolNode(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      let result;
      
      // Validate tool connection first
      const connectionValidation = await validateToolConnection(context.userId, nodeConfig.toolType);
      if (!connectionValidation.isValid) {
        throw new Error(connectionValidation.error || 'Tool not connected');
      }

      switch (nodeConfig.toolType) {
        case 'google_drive':
          result = await this.executeGoogleDrive(nodeConfig, context);
          break;

        case 'google_docs':
          result = await this.executeGoogleDocs(nodeConfig, context);
          break;

        case 'google_sheets':
          result = await this.executeGoogleSheets(nodeConfig, context);
          break;

        case 'gmail':
          result = await this.executeGmail(nodeConfig, context);
          break;

        case 'calendar':
          result = await this.executeGoogleCalendar(nodeConfig, context);
          break;

        case 'youtube':
          result = await this.executeYouTube(nodeConfig, context);
          break;

        case 'notion':
          result = await this.executeNotion(nodeConfig, context);
          break;

        case 'supabase':
          result = await this.executeSupabase(nodeConfig, context);
          break;

        default:
          throw new Error(`Unknown tool type: ${nodeConfig.toolType}`);
      }

      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result.data,
        toolType: nodeConfig.toolType,
        executionTime,
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        data: null,
        toolType: nodeConfig.toolType || 'unknown',
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Helper method to validate tool configuration
  static validateToolConfig(nodeConfig: ToolNodeData['config']): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!nodeConfig.toolType) {
      errors.push('Tool type is required');
    }

    switch (nodeConfig.toolType) {
      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'notion':
      case 'calendar':
      case 'gmail':
      case 'youtube':
      case 'supabase':
        if (!nodeConfig.isAuthenticated || nodeConfig.connectionStatus !== 'connected') {
          errors.push(`${nodeConfig.toolType} requires authentication and connection`);
        }
        break;

      default:
        errors.push(`Unknown tool type: ${nodeConfig.toolType}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get human-readable description of what the tool will do
  static getToolDescription(nodeConfig: ToolNodeData['config']): string {
    switch (nodeConfig.toolType) {
      case 'google_drive':
        return 'Access and manage Google Drive files';
      case 'google_docs':
        return 'Create and edit Google Documents';
      case 'google_sheets':
        return 'Work with Google Spreadsheets';
      case 'zapier':
        return 'Connect with 5000+ apps via Zapier';
      case 'notion':
        return 'Access Notion databases and pages';
      case 'calendar':
        return 'Manage calendar events and schedules';
      case 'gmail':
        return 'Send and manage emails';
      case 'youtube':
        return 'Access YouTube data and analytics';
      case 'supabase':
        return 'Direct database operations';
      default:
        return 'External tool integration';
    }
  }

  // Get example usage for the tool
  static getToolExampleUsage(nodeConfig: ToolNodeData['config']): string {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return 'Example: "search for latest AI news" or "find information about climate change"';
      case 'google_drive':
        return 'Example: "list files in folder" or "upload document"';
      case 'google_docs':
        return 'Example: "create new document" or "edit document content"';
      case 'google_sheets':
        return 'Example: "add row to spreadsheet" or "calculate sum"';
      case 'zapier':
        return 'Example: "trigger workflow" or "send data to app"';
      case 'notion':
        return 'Example: "create page" or "query database"';
      case 'calendar':
        return 'Example: "schedule meeting" or "check availability"';
      case 'gmail':
        return 'Example: "send email" or "check inbox"';
      case 'youtube':
        return 'Example: "get video stats" or "upload video"';
      case 'supabase':
        return 'Example: "query table" or "insert record"';
      default:
        return 'Tool-specific input required';
    }
  }

  // Check if tool is ready to use
  static isToolReady(nodeConfig: ToolNodeData['config']): boolean {
    return nodeConfig.isAuthenticated && nodeConfig.connectionStatus === 'connected';
  }

  // Get tool status
  static getToolStatus(nodeConfig: ToolNodeData['config']): {
    status: 'ready' | 'needs_auth' | 'error' | 'expired';
    message: string;
  } {
    if (!nodeConfig.toolType) {
      return {
        status: 'error',
        message: 'No tool type selected'
      };
    }

    switch (nodeConfig.toolType) {
      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'notion':
      case 'calendar':
      case 'gmail':
      case 'youtube':
      case 'supabase':
        if (!nodeConfig.isAuthenticated) {
          return {
            status: 'needs_auth',
            message: `${nodeConfig.toolType} requires authentication`
          };
        }
        if (nodeConfig.connectionStatus === 'expired' || nodeConfig.connectionStatus === 'revoked') {
          return {
            status: 'expired',
            message: `${nodeConfig.toolType} connection has expired`
          };
        }
        if (nodeConfig.connectionStatus === 'connected') {
          return {
            status: 'ready',
            message: `${nodeConfig.toolType} is ready to use`
          };
        }
        return {
          status: 'error',
          message: `${nodeConfig.toolType} connection error`
        };
      default:
        return {
          status: 'error',
          message: 'Unknown tool type'
        };
    }
  }

  /**
   * Execute Google Drive tool
   */
  private static async executeGoogleDrive(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📁 Executing Google Drive tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      // Parse user input to determine action
      const action = this.parseGoogleDriveAction(userInput);

      switch (action.type) {
        case 'list_files':
          return await GoogleDriveAPI.listFiles(userId, action.params, timeout);
        case 'get_file':
          return await GoogleDriveAPI.getFile(userId, action.params, timeout);
        case 'create_file':
          return await GoogleDriveAPI.createFile(userId, action.params, timeout);
        case 'search_files':
          return await GoogleDriveAPI.searchFiles(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Drive action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Drive tool error:', error);
      throw new Error(`Google Drive operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Google Docs tool
   */
  private static async executeGoogleDocs(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📄 Executing Google Docs tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGoogleDocsAction(userInput);

      switch (action.type) {
        case 'create_document':
          return await GoogleDocsAPI.createDocument(userId, action.params, timeout);
        case 'get_document':
          return await GoogleDocsAPI.getDocument(userId, action.params, timeout);
        case 'update_document':
          return await GoogleDocsAPI.updateDocument(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Docs action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Docs tool error:', error);
      throw new Error(`Google Docs operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Google Sheets tool
   */
  private static async executeGoogleSheets(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📊 Executing Google Sheets tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGoogleSheetsAction(userInput);

      switch (action.type) {
        case 'create_spreadsheet':
          return await GoogleSheetsAPI.createSpreadsheet(userId, action.params, timeout);
        case 'get_spreadsheet':
          return await GoogleSheetsAPI.getSpreadsheet(userId, action.params, timeout);
        case 'update_cells':
          return await GoogleSheetsAPI.updateCells(userId, action.params, timeout);
        case 'read_range':
          return await GoogleSheetsAPI.readRange(userId, action.params, timeout);
        case 'append_row':
          return await GoogleSheetsAPI.appendRow(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Sheets action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Sheets tool error:', error);
      throw new Error(`Google Sheets operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Gmail tool
   */
  private static async executeGmail(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📧 Executing Gmail tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGmailAction(userInput);

      switch (action.type) {
        case 'send_email':
          return await GmailAPI.sendEmail(userId, action.params, timeout);
        case 'list_emails':
          return await GmailAPI.listEmails(userId, action.params, timeout);
        case 'get_email':
          return await GmailAPI.getEmail(userId, action.params, timeout);
        case 'search_emails':
          return await GmailAPI.searchEmails(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Gmail action: ${action.type}`);
      }
    } catch (error) {
      console.error('Gmail tool error:', error);
      throw new Error(`Gmail operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Google Calendar tool
   */
  private static async executeGoogleCalendar(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📅 Executing Google Calendar tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGoogleCalendarAction(userInput);

      switch (action.type) {
        case 'create_event':
          return await GoogleCalendarAPI.createEvent(userId, action.params, timeout);
        case 'list_events':
          return await GoogleCalendarAPI.listEvents(userId, action.params, timeout);
        case 'get_event':
          return await GoogleCalendarAPI.getEvent(userId, action.params, timeout);
        case 'update_event':
          return await GoogleCalendarAPI.updateEvent(userId, action.params, timeout);
        case 'delete_event':
          return await GoogleCalendarAPI.deleteEvent(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Calendar action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Calendar tool error:', error);
      throw new Error(`Google Calendar operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute YouTube tool
   */
  private static async executeYouTube(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📺 Executing YouTube tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseYouTubeAction(userInput);

      switch (action.type) {
        case 'search_videos':
          return await YouTubeAPI.searchVideos(userId, action.params, timeout);
        case 'get_video':
          return await YouTubeAPI.getVideo(userId, action.params, timeout);
        case 'get_channel':
          return await YouTubeAPI.getChannel(userId, action.params, timeout);
        case 'get_analytics':
          return await YouTubeAPI.getAnalytics(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported YouTube action: ${action.type}`);
      }
    } catch (error) {
      console.error('YouTube tool error:', error);
      throw new Error(`YouTube operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Notion tool
   */
  private static async executeNotion(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📝 Executing Notion tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseNotionAction(userInput);

      switch (action.type) {
        case 'create_page':
          return await NotionAPI.createPage(userId, action.params, timeout);
        case 'get_page':
          return await NotionAPI.getPage(userId, action.params, timeout);
        case 'update_page':
          return await NotionAPI.updatePage(userId, action.params, timeout);
        case 'query_database':
          return await NotionAPI.queryDatabase(userId, action.params, timeout);
        case 'create_database_entry':
          return await NotionAPI.createDatabaseEntry(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Notion action: ${action.type}`);
      }
    } catch (error) {
      console.error('Notion tool error:', error);
      throw new Error(`Notion operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Supabase tool
   */
  private static async executeSupabase(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('🗄️ Executing Supabase tool');

    const { userInput, userId } = context;

    try {
      // For Supabase, we'll use a simple implementation since it's the user's own database
      const action = this.parseSupabaseAction(userInput);

      // Note: Supabase tool would typically use the user's own Supabase instance
      // For now, we'll return a placeholder response
      return {
        success: true,
        action: action.type,
        message: 'Supabase integration requires custom configuration',
        data: null
      };
    } catch (error) {
      console.error('Supabase tool error:', error);
      throw new Error(`Supabase operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Action parsers - these interpret user input and determine what API calls to make
  private static parseGoogleDriveAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('list') || input.includes('show files')) {
      return { type: 'list_files', params: { limit: 10 } };
    }
    if (input.includes('search')) {
      const query = userInput.replace(/search\s+for\s+/i, '').replace(/search\s+/i, '');
      return { type: 'search_files', params: { query } };
    }
    if (input.includes('create') || input.includes('new file')) {
      const name = userInput.replace(/create\s+file\s+/i, '').replace(/new\s+file\s+/i, '');
      return { type: 'create_file', params: { name, content: '' } };
    }
    if (input.includes('get file') || input.includes('download')) {
      // Extract file ID from input (simplified)
      return { type: 'get_file', params: { fileId: 'extracted_file_id' } };
    }

    // Default to listing files
    return { type: 'list_files', params: { limit: 10 } };
  }

  private static parseGoogleDocsAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('create') || input.includes('new document')) {
      const title = userInput.replace(/create\s+document\s+/i, '').replace(/new\s+document\s+/i, '') || 'Untitled Document';
      return { type: 'create_document', params: { title } };
    }
    if (input.includes('get') || input.includes('open')) {
      return { type: 'get_document', params: { documentId: 'extracted_doc_id' } };
    }
    if (input.includes('update') || input.includes('edit')) {
      return { type: 'update_document', params: { documentId: 'extracted_doc_id', requests: [] } };
    }

    // Default to creating a document
    return { type: 'create_document', params: { title: 'New Document' } };
  }

  private static parseGoogleSheetsAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('create') || input.includes('new spreadsheet')) {
      const title = userInput.replace(/create\s+spreadsheet\s+/i, '').replace(/new\s+spreadsheet\s+/i, '') || 'Untitled Spreadsheet';
      return { type: 'create_spreadsheet', params: { title } };
    }
    if (input.includes('read') || input.includes('get range')) {
      return { type: 'read_range', params: { spreadsheetId: 'extracted_sheet_id', range: 'A1:Z100' } };
    }
    if (input.includes('append') || input.includes('add row')) {
      return { type: 'append_row', params: { spreadsheetId: 'extracted_sheet_id', range: 'A1', values: [] } };
    }
    if (input.includes('update') || input.includes('edit cells')) {
      return { type: 'update_cells', params: { spreadsheetId: 'extracted_sheet_id', range: 'A1', values: [] } };
    }

    // Default to creating a spreadsheet
    return { type: 'create_spreadsheet', params: { title: 'New Spreadsheet' } };
  }

  private static parseGmailAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('send') || input.includes('email')) {
      // Extract email details (simplified)
      return {
        type: 'send_email',
        params: {
          to: '<EMAIL>',
          subject: 'Subject from AI',
          body: userInput
        }
      };
    }
    if (input.includes('list') || input.includes('inbox')) {
      return { type: 'list_emails', params: { maxResults: 10 } };
    }
    if (input.includes('search')) {
      const query = userInput.replace(/search\s+for\s+/i, '').replace(/search\s+/i, '');
      return { type: 'search_emails', params: { query, maxResults: 10 } };
    }
    if (input.includes('get') || input.includes('read')) {
      return { type: 'get_email', params: { messageId: 'extracted_message_id' } };
    }

    // Default to listing emails
    return { type: 'list_emails', params: { maxResults: 10 } };
  }

  private static parseGoogleCalendarAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('create') || input.includes('schedule') || input.includes('new event')) {
      return {
        type: 'create_event',
        params: {
          summary: 'Event from AI',
          start: new Date().toISOString(),
          end: new Date(Date.now() + 3600000).toISOString() // 1 hour later
        }
      };
    }
    if (input.includes('list') || input.includes('events')) {
      return { type: 'list_events', params: { maxResults: 10 } };
    }
    if (input.includes('get') || input.includes('show event')) {
      return { type: 'get_event', params: { eventId: 'extracted_event_id' } };
    }
    if (input.includes('update') || input.includes('edit')) {
      return { type: 'update_event', params: { eventId: 'extracted_event_id' } };
    }
    if (input.includes('delete') || input.includes('cancel')) {
      return { type: 'delete_event', params: { eventId: 'extracted_event_id' } };
    }

    // Default to listing events
    return { type: 'list_events', params: { maxResults: 10 } };
  }

  private static parseYouTubeAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('search')) {
      const query = userInput.replace(/search\s+for\s+/i, '').replace(/search\s+/i, '');
      return { type: 'search_videos', params: { query, maxResults: 10 } };
    }
    if (input.includes('get video') || input.includes('video details')) {
      return { type: 'get_video', params: { videoId: 'extracted_video_id' } };
    }
    if (input.includes('channel') || input.includes('get channel')) {
      return { type: 'get_channel', params: { channelId: 'extracted_channel_id' } };
    }
    if (input.includes('analytics') || input.includes('stats')) {
      return {
        type: 'get_analytics',
        params: {
          channelId: 'extracted_channel_id',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        }
      };
    }

    // Default to searching videos
    return { type: 'search_videos', params: { query: userInput, maxResults: 10 } };
  }

  private static parseNotionAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('create page') || input.includes('new page')) {
      return {
        type: 'create_page',
        params: {
          parent: { page_id: 'parent_page_id' },
          properties: { title: { title: [{ text: { content: 'New Page' } }] } }
        }
      };
    }
    if (input.includes('get page') || input.includes('read page')) {
      return { type: 'get_page', params: { pageId: 'extracted_page_id' } };
    }
    if (input.includes('update') || input.includes('edit')) {
      return { type: 'update_page', params: { pageId: 'extracted_page_id', properties: {} } };
    }
    if (input.includes('query') || input.includes('database')) {
      return { type: 'query_database', params: { databaseId: 'extracted_db_id' } };
    }
    if (input.includes('create entry') || input.includes('add to database')) {
      return {
        type: 'create_database_entry',
        params: {
          databaseId: 'extracted_db_id',
          properties: {}
        }
      };
    }

    // Default to creating a page
    return {
      type: 'create_page',
      params: {
        parent: { page_id: 'parent_page_id' },
        properties: { title: { title: [{ text: { content: userInput } }] } }
      }
    };
  }

  private static parseSupabaseAction(userInput: string): { type: string; params: any } {
    const input = userInput.toLowerCase();

    if (input.includes('query') || input.includes('select')) {
      return { type: 'query_table', params: { table: 'extracted_table', query: {} } };
    }
    if (input.includes('insert') || input.includes('add')) {
      return { type: 'insert_record', params: { table: 'extracted_table', data: {} } };
    }
    if (input.includes('update') || input.includes('edit')) {
      return { type: 'update_record', params: { table: 'extracted_table', id: 'extracted_id', data: {} } };
    }
    if (input.includes('delete') || input.includes('remove')) {
      return { type: 'delete_record', params: { table: 'extracted_table', id: 'extracted_id' } };
    }

    // Default to querying
    return { type: 'query_table', params: { table: 'default_table', query: {} } };
  }
}
