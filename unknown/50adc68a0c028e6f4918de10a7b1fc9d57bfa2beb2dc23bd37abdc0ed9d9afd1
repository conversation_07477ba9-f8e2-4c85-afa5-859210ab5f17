// Google OAuth Authorization Endpoint
// Initiates OAuth flow for Google tools (Drive, Docs, Sheets, Gmail, Calendar, YouTube)

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { generateAuthUrl } from '@/lib/oauth/config';
import crypto from 'crypto';

export async function GET(request: NextRequest) {
  try {
    console.log('🔐 GOOGLE OAUTH: Authorization request started');
    
    // Get authenticated user
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('🔐 GOOGLE OAUTH: User not authenticated:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get tool type from query params
    const { searchParams } = new URL(request.url);
    const toolType = searchParams.get('tool');
    const returnUrl = searchParams.get('returnUrl') || '/manual-build';
    
    if (!toolType) {
      return NextResponse.json(
        { error: 'Tool type is required' },
        { status: 400 }
      );
    }
    
    // Validate tool type
    const validGoogleTools = ['google_drive', 'google_docs', 'google_sheets', 'gmail', 'calendar', 'youtube'];
    if (!validGoogleTools.includes(toolType)) {
      return NextResponse.json(
        { error: 'Invalid Google tool type' },
        { status: 400 }
      );
    }
    
    console.log(`🔐 GOOGLE OAUTH: Authorizing ${toolType} for user ${user.id}`);
    
    // Generate state parameter for CSRF protection
    const state = crypto.randomBytes(32).toString('hex');
    const stateData = {
      userId: user.id,
      toolType,
      returnUrl,
      timestamp: Date.now()
    };
    
    // Store state in session/database for verification
    const { error: stateError } = await supabase
      .from('oauth_states')
      .insert({
        state,
        user_id: user.id,
        tool_type: toolType,
        return_url: returnUrl,
        expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes
      });
    
    if (stateError) {
      console.error('🔐 GOOGLE OAUTH: Error storing state:', stateError);
      // Continue anyway, state verification is optional
    }
    
    // Generate authorization URL
    const authUrl = generateAuthUrl(toolType, state);
    if (!authUrl) {
      console.error('🔐 GOOGLE OAUTH: Failed to generate auth URL');
      return NextResponse.json(
        { error: 'OAuth configuration error' },
        { status: 500 }
      );
    }
    
    console.log('🔐 GOOGLE OAUTH: Redirecting to Google authorization');
    
    // Return the authorization URL for client-side redirect
    return NextResponse.json({
      authUrl,
      state,
      toolType
    });
    
  } catch (error) {
    console.error('🔐 GOOGLE OAUTH: Authorization error:', error);
    return NextResponse.json(
      { error: 'OAuth authorization failed' },
      { status: 500 }
    );
  }
}

// Handle POST requests for programmatic authorization
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { toolType, returnUrl } = body;
    
    // Create a new request with query parameters
    const url = new URL(request.url);
    url.searchParams.set('tool', toolType);
    if (returnUrl) {
      url.searchParams.set('returnUrl', returnUrl);
    }
    
    const newRequest = new NextRequest(url, {
      method: 'GET',
      headers: request.headers
    });
    
    return GET(newRequest);
  } catch (error) {
    console.error('🔐 GOOGLE OAUTH: POST authorization error:', error);
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    );
  }
}
