'use client';

import { UserGroupIcon } from '@heroicons/react/24/outline';
import BaseNode from './BaseNode';
import { WorkflowNode, RoleAgentNodeData } from '@/types/manualBuild';

interface RoleAgentNodeProps {
  data: WorkflowNode['data'];
}

export default function RoleAgentNode({ data }: RoleAgentNodeProps) {
  const config = data.config as RoleAgentNodeData['config'];
  const roleName = config?.roleName;
  const tools = config?.tools || [];

  return (
    <BaseNode
      data={data}
      icon={UserGroupIcon}
      color="#8b5cf6"
      hasInput={false}
      hasOutput={true}
      outputLabel="Role"
    >
      <div className="space-y-3">
        {roleName ? (
          <div className="space-y-2">
            <div className="text-sm font-medium text-white">
              {roleName}
            </div>
            
            {config?.customPrompt && (
              <div className="text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded">
                Custom prompt configured
              </div>
            )}
            
            {tools.length > 0 && (
              <div className="space-y-1">
                <div className="text-xs text-gray-400">Tools:</div>
                <div className="flex flex-wrap gap-1">
                  {tools.slice(0, 3).map((tool, index) => (
                    <span key={index} className="text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded">
                      {tool}
                    </span>
                  ))}
                  {tools.length > 3 && (
                    <span className="text-xs text-gray-400">
                      +{tools.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            )}
            
            {config?.memoryEnabled && (
              <div className="text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded">
                ✓ Memory enabled
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              Role Plugin
            </div>
            <div className="text-xs text-gray-400">
              Connect to AI Provider nodes to assign specialized roles (e.g., Coder, Writer, Analyst).
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
