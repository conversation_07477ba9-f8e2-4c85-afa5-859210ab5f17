// OAuth Token Refresh API
// Manually refresh OAuth tokens for tools

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { refreshOAuthToken, getOAuthTokens } from '@/lib/oauth/tokenManager';

export async function POST(request: NextRequest) {
  try {
    console.log('🔐 TOKEN REFRESH: Manual token refresh requested');
    
    // Get authenticated user
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('🔐 TOKEN REFRESH: User not authenticated:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Get tool type from request body
    const body = await request.json();
    const { toolType } = body;
    
    if (!toolType) {
      return NextResponse.json(
        { error: 'Tool type is required' },
        { status: 400 }
      );
    }
    
    console.log(`🔐 TOKEN REFRESH: Refreshing tokens for ${toolType} for user ${user.id}`);
    
    // Check if tool is connected
    const existingTokens = await getOAuthTokens(user.id, toolType);
    if (!existingTokens) {
      return NextResponse.json(
        { error: 'Tool not connected' },
        { status: 404 }
      );
    }
    
    // Attempt to refresh the token
    const refreshResult = await refreshOAuthToken(user.id, toolType);
    
    if (!refreshResult.success) {
      console.error('🔐 TOKEN REFRESH: Token refresh failed:', refreshResult.error);
      return NextResponse.json(
        { error: refreshResult.error || 'Token refresh failed' },
        { status: 400 }
      );
    }
    
    console.log(`🔐 TOKEN REFRESH: Successfully refreshed tokens for ${toolType}`);
    
    return NextResponse.json({
      success: true,
      message: 'Token refreshed successfully',
      expires_at: refreshResult.expires_at
    });
    
  } catch (error) {
    console.error('🔐 TOKEN REFRESH: Error refreshing token:', error);
    return NextResponse.json(
      { error: 'Token refresh failed' },
      { status: 500 }
    );
  }
}
