'use client';

import { DocumentTextIcon } from '@heroicons/react/24/outline';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

interface OutputNodeProps {
  data: WorkflowNode['data'];
}

export default function OutputNode({ data }: OutputNodeProps) {
  return (
    <BaseNode
      data={data}
      icon={DocumentTextIcon}
      color="#ef4444"
      hasInput={true}
      hasOutput={false}
    >
      <div className="space-y-2">
        <div className="text-sm text-gray-300">
          Final response output
        </div>
        <div className="text-xs text-gray-400">
          This node formats and delivers the final response to the user. Every workflow must end here.
        </div>
        {data.config?.outputFormat && (
          <div className="mt-2 px-2 py-1 bg-red-900/30 rounded text-xs text-red-300">
            Format: {data.config.outputFormat}
          </div>
        )}
      </div>
    </BaseNode>
  );
}
