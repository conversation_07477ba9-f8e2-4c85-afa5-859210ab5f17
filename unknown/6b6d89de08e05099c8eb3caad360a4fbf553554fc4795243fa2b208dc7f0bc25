'use client';

import { EyeIcon } from '@heroicons/react/24/outline';
import { useEdges, useNodes } from '@xyflow/react';
import BaseNode from './BaseNode';
import { WorkflowNode, VisionNodeData } from '@/types/manualBuild';

const providerColors = {
  openai: '#10b981',
  anthropic: '#f97316', 
  google: '#3b82f6',
  deepseek: '#8b5cf6',
  xai: '#374151',
  openrouter: 'linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)'
};

const providerNames = {
  openai: 'OpenAI',
  anthropic: 'Anthropic',
  google: 'Google',
  deepseek: 'DeepSeek',
  xai: 'xAI (Grok)',
  openrouter: 'OpenRouter'
};

interface VisionNodeProps {
  data: WorkflowNode['data'];
  id: string;
}

export default function VisionNode({ data, id }: VisionNodeProps) {
  const edges = useEdges();
  const nodes = useNodes();
  const config = data.config as VisionNodeData['config'];
  const providerId = config?.providerId;
  const modelId = config?.modelId;
  const color = providerId ? providerColors[providerId] : '#8b5cf6'; // Purple default for vision
  const providerName = providerId ? providerNames[providerId] : 'Vision AI';

  // Get connected role nodes - using reactive hooks for automatic re-renders
  const connectedRoles = edges
    .filter(edge => edge.target === id && edge.targetHandle === 'role')
    .map(edge => {
      // Find the source node to get role information
      const sourceNode = nodes.find(node => node.id === edge.source);
      if (sourceNode && sourceNode.type === 'roleAgent') {
        const roleConfig = sourceNode.data.config as any;
        console.log('Vision Node - Role Agent Data:', sourceNode.data); // Debug log
        console.log('Vision Node - Role Config:', roleConfig); // Debug log
        return {
          id: edge.source,
          name: roleConfig?.roleName || sourceNode.data.label || 'Unknown Role',
          type: roleConfig?.roleType || 'predefined'
        };
      }
      return null;
    })
    .filter(Boolean);

  return (
    <BaseNode
      data={data}
      icon={EyeIcon}
      color={typeof color === 'string' ? color : '#8b5cf6'}
      hasInput={false}
      hasOutput={true}
      hasRoleInput={true}
      hasToolsInput={false}
    >
      <div className="space-y-3">
        {providerId ? (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-white">
                {providerName}
              </span>
              <span className="text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full">
                Vision
              </span>
            </div>
            
            {modelId && (
              <div className="text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded">
                Model: {modelId}
              </div>
            )}
            
            {config?.parameters && (
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="text-gray-400">
                  Temp: {config.parameters.temperature || 1.0}
                </div>
                <div className="text-gray-400">
                  Max: {config.parameters.maxTokens || 'Auto'}
                </div>
              </div>
            )}
            
            {config?.fallbackProvider && (
              <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
                Fallback: {providerNames[config.fallbackProvider.providerId as keyof typeof providerNames]}
              </div>
            )}

            {/* Connected Role Tags */}
            {connectedRoles.length > 0 && (
              <div className="space-y-1">
                <div className="text-xs text-gray-400">Roles:</div>
                <div className="flex flex-wrap gap-1">
                  {connectedRoles.map((role: any) => (
                    <span
                      key={role.id}
                      className="text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30"
                    >
                      {role.name}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-gray-300">
              Vision AI Connection
            </div>
            <div className="text-xs text-gray-400">
              Configure to connect to multimodal AI models for image analysis and vision tasks.
            </div>
            <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
              ⚠️ Needs configuration
            </div>
          </div>
        )}
      </div>
    </BaseNode>
  );
}
