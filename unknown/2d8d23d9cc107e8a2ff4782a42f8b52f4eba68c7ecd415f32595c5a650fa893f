/**
 * Workflow Management API
 * Handles CRUD operations for workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowPersistence } from '@/lib/workflow/WorkflowPersistence';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('id');

    if (workflowId) {
      // Get specific workflow
      const workflow = await workflowPersistence.getWorkflow(workflowId, user.id);
      if (!workflow) {
        return NextResponse.json({ error: 'Workflow not found' }, { status: 404 });
      }

      // Get API key info
      const apiKeyInfo = await workflowPersistence.getWorkflowAPIKey(workflowId, user.id);

      return NextResponse.json({
        workflow,
        api_key_info: apiKeyInfo ? {
          id: apiKeyInfo.id,
          key_name: apiKeyInfo.key_name,
          key_prefix: apiKeyInfo.key_prefix,
          encrypted_suffix: apiKeyInfo.encrypted_key_suffix,
          status: apiKeyInfo.status,
          total_requests: apiKeyInfo.total_requests,
          last_used_at: apiKeyInfo.last_used_at,
          created_at: apiKeyInfo.created_at
        } : null
      });
    } else {
      // Get all user workflows
      const workflows = await workflowPersistence.getUserWorkflows(user.id);
      
      return NextResponse.json({
        workflows: workflows.map(w => ({
          id: w.id,
          name: w.name,
          description: w.description,
          is_active: w.is_active,
          version: w.version,
          created_at: w.created_at,
          updated_at: w.updated_at,
          node_count: w.nodes.length,
          edge_count: w.edges.length
        }))
      });
    }

  } catch (error) {
    console.error('Workflow GET error:', error);
    return NextResponse.json({
      error: 'Failed to fetch workflows',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, nodes, edges, settings } = body;

    if (!name || !nodes || !edges) {
      return NextResponse.json({
        error: 'Missing required fields: name, nodes, edges'
      }, { status: 400 });
    }

    // Validate nodes structure
    if (!Array.isArray(nodes) || nodes.length === 0) {
      return NextResponse.json({
        error: 'Nodes must be a non-empty array'
      }, { status: 400 });
    }

    // Check for User Request node (required entry point)
    const hasUserRequestNode = nodes.some(node => node.type === 'userRequest');
    if (!hasUserRequestNode) {
      return NextResponse.json({
        error: 'Workflow must contain at least one User Request node'
      }, { status: 400 });
    }

    // Save workflow and generate API key
    const { workflow, apiKey } = await workflowPersistence.saveWorkflow(
      user.id,
      name,
      description || '',
      nodes,
      edges,
      settings || {}
    );

    return NextResponse.json({
      success: true,
      workflow: {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description,
        is_active: workflow.is_active,
        version: workflow.version,
        created_at: workflow.created_at,
        updated_at: workflow.updated_at
      },
      api_key: apiKey, // Only shown once!
      message: 'Workflow saved successfully. Save your API key - it will not be shown again!'
    });

  } catch (error) {
    console.error('Workflow POST error:', error);
    return NextResponse.json({
      error: 'Failed to save workflow',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, description, nodes, edges, settings, is_active } = body;

    if (!id) {
      return NextResponse.json({
        error: 'Workflow ID is required'
      }, { status: 400 });
    }

    // Update workflow
    const workflow = await workflowPersistence.updateWorkflow(id, user.id, {
      name,
      description,
      nodes,
      edges,
      settings,
      is_active
    });

    return NextResponse.json({
      success: true,
      workflow: {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description,
        is_active: workflow.is_active,
        version: workflow.version,
        updated_at: workflow.updated_at
      }
    });

  } catch (error) {
    console.error('Workflow PUT error:', error);
    return NextResponse.json({
      error: 'Failed to update workflow',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('id');

    if (!workflowId) {
      return NextResponse.json({
        error: 'Workflow ID is required'
      }, { status: 400 });
    }

    // Delete workflow and its API key
    await workflowPersistence.deleteWorkflow(workflowId, user.id);

    return NextResponse.json({
      success: true,
      message: 'Workflow deleted successfully'
    });

  } catch (error) {
    console.error('Workflow DELETE error:', error);
    return NextResponse.json({
      error: 'Failed to delete workflow',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
