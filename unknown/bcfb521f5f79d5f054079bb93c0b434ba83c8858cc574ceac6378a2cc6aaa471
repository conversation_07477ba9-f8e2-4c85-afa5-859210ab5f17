'use client';

import { useState, useEffect } from 'react';
import { 
  ShareIcon,
  LinkIcon,
  UserGroupIcon,
  GlobeAltIcon,
  ClipboardDocumentIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  ShieldCheckIcon,
  CalendarIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { CheckIcon } from '@heroicons/react/24/solid';

interface WorkflowSharingModalProps {
  workflowId: string;
  workflowName: string;
  isOpen: boolean;
  onClose: () => void;
}

interface Share {
  id: string;
  shared_with?: string;
  permission_level: 'view' | 'edit' | 'admin';
  share_token: string;
  expires_at?: string;
  is_public: boolean;
  created_at: string;
}

export default function WorkflowSharingModal({
  workflowId,
  workflowName,
  isOpen,
  onClose
}: WorkflowSharingModalProps) {
  const [shares, setShares] = useState<Share[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [shareEmail, setShareEmail] = useState('');
  const [sharePermission, setSharePermission] = useState<'view' | 'edit' | 'admin'>('view');
  const [isPublic, setIsPublic] = useState(false);
  const [expiresAt, setExpiresAt] = useState('');
  const [copiedToken, setCopiedToken] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadShares();
    }
  }, [isOpen, workflowId]);

  const loadShares = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/manual-build/workflows/${workflowId}/shares`);
      if (response.ok) {
        const data = await response.json();
        setShares(data.shares || []);
      }
    } catch (error) {
      console.error('Failed to load shares:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateShare = async () => {
    if (!shareEmail && !isPublic) return;

    try {
      const response = await fetch(`/api/manual-build/workflows/${workflowId}/shares`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sharedWith: shareEmail ? [shareEmail] : undefined,
          permissionLevel: sharePermission,
          isPublic,
          expiresAt: expiresAt ? new Date(expiresAt).toISOString() : undefined
        }),
      });

      if (response.ok) {
        setShareEmail('');
        setIsPublic(false);
        setExpiresAt('');
        await loadShares();
      }
    } catch (error) {
      console.error('Failed to create share:', error);
    }
  };

  const handleRevokeShare = async (shareId: string) => {
    try {
      const response = await fetch(`/api/manual-build/workflows/${workflowId}/shares/${shareId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadShares();
      }
    } catch (error) {
      console.error('Failed to revoke share:', error);
    }
  };

  const copyShareLink = async (shareToken: string) => {
    const shareUrl = `${window.location.origin}/manual-build/shared/${shareToken}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopiedToken(shareToken);
      setTimeout(() => setCopiedToken(null), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'admin':
        return <ShieldCheckIcon className="w-4 h-4 text-red-400" />;
      case 'edit':
        return <PencilIcon className="w-4 h-4 text-yellow-400" />;
      case 'view':
      default:
        return <EyeIcon className="w-4 h-4 text-blue-400" />;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'admin':
        return 'text-red-400 bg-red-900/20 border-red-700/30';
      case 'edit':
        return 'text-yellow-400 bg-yellow-900/20 border-yellow-700/30';
      case 'view':
      default:
        return 'text-blue-400 bg-blue-900/20 border-blue-700/30';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <ShareIcon className="w-6 h-6 text-[#ff6b35]" />
            <div>
              <h2 className="text-xl font-bold text-white">Share Workflow</h2>
              <p className="text-gray-400 text-sm">{workflowName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Create New Share */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Create New Share</h3>
            
            <div className="space-y-4">
              {/* Share Type */}
              <div className="flex gap-4">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="shareType"
                    checked={!isPublic}
                    onChange={() => setIsPublic(false)}
                    className="text-[#ff6b35] focus:ring-[#ff6b35]"
                  />
                  <UserGroupIcon className="w-5 h-5 text-gray-400" />
                  <span className="text-white">Share with specific users</span>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="shareType"
                    checked={isPublic}
                    onChange={() => setIsPublic(true)}
                    className="text-[#ff6b35] focus:ring-[#ff6b35]"
                  />
                  <GlobeAltIcon className="w-5 h-5 text-gray-400" />
                  <span className="text-white">Public link</span>
                </label>
              </div>

              {/* Email Input (if not public) */}
              {!isPublic && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={shareEmail}
                    onChange={(e) => setShareEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"
                  />
                </div>
              )}

              {/* Permission Level */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Permission Level
                </label>
                <select
                  value={sharePermission}
                  onChange={(e) => setSharePermission(e.target.value as any)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"
                >
                  <option value="view">View Only</option>
                  <option value="edit">Can Edit</option>
                  <option value="admin">Admin Access</option>
                </select>
              </div>

              {/* Expiration Date */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Expires At (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={expiresAt}
                  onChange={(e) => setExpiresAt(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"
                />
              </div>

              {/* Create Button */}
              <button
                onClick={handleCreateShare}
                disabled={!shareEmail && !isPublic}
                className="w-full px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create Share Link
              </button>
            </div>
          </div>

          {/* Existing Shares */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Active Shares</h3>
            
            {isLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">Loading shares...</div>
              </div>
            ) : shares.length === 0 ? (
              <div className="text-center py-8">
                <ShareIcon className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <div className="text-gray-400">No active shares</div>
              </div>
            ) : (
              <div className="space-y-3">
                {shares.map((share) => (
                  <div
                    key={share.id}
                    className="bg-gray-700/50 border border-gray-600 rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1">
                        {share.is_public ? (
                          <GlobeAltIcon className="w-5 h-5 text-green-400" />
                        ) : (
                          <UserGroupIcon className="w-5 h-5 text-blue-400" />
                        )}
                        
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-white font-medium">
                              {share.is_public ? 'Public Link' : share.shared_with || 'Unknown User'}
                            </span>
                            <span className={`px-2 py-0.5 text-xs rounded-full border ${getPermissionColor(share.permission_level)}`}>
                              {getPermissionIcon(share.permission_level)}
                              <span className="ml-1 capitalize">{share.permission_level}</span>
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-4 text-xs text-gray-400">
                            <span>Created {new Date(share.created_at).toLocaleDateString()}</span>
                            {share.expires_at && (
                              <span className="flex items-center gap-1">
                                <CalendarIcon className="w-3 h-3" />
                                Expires {new Date(share.expires_at).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => copyShareLink(share.share_token)}
                          className="p-2 text-gray-400 hover:text-white transition-colors"
                          title="Copy share link"
                        >
                          {copiedToken === share.share_token ? (
                            <CheckIcon className="w-4 h-4 text-green-400" />
                          ) : (
                            <ClipboardDocumentIcon className="w-4 h-4" />
                          )}
                        </button>
                        
                        <button
                          onClick={() => handleRevokeShare(share.id)}
                          className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                          title="Revoke share"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
