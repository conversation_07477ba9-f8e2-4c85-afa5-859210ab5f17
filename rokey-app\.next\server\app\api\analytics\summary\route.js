/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/summary/route";
exports.ids = ["app/api/analytics/summary/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_summary_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/summary/route.ts */ \"(rsc)/./src/app/api/analytics/summary/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/summary/route\",\n        pathname: \"/api/analytics/summary\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/summary/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\summary\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_summary_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZhbmFseXRpY3MlMkZzdW1tYXJ5JTJGcm91dGUmcGFnZT0lMkZhcGklMkZhbmFseXRpY3MlMkZzdW1tYXJ5JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYW5hbHl0aWNzJTJGc3VtbWFyeSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUm9LZXklMjBBcHAlNUNyb2tleS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNSb0tleSUyMEFwcCU1Q3Jva2V5LWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDcUI7QUFDbEc7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYW5hbHl0aWNzXFxcXHN1bW1hcnlcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2FuYWx5dGljcy9zdW1tYXJ5L3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYW5hbHl0aWNzL3N1bW1hcnlcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2FuYWx5dGljcy9zdW1tYXJ5L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcUm9LZXkgQXBwXFxcXHJva2V5LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxhbmFseXRpY3NcXFxcc3VtbWFyeVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/summary/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/analytics/summary/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n// Schema for query parameters\nconst AnalyticsQuerySchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    startDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime({\n        offset: true\n    }).optional(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().datetime({\n        offset: true\n    }).optional(),\n    customApiConfigId: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().uuid().optional(),\n    groupBy: zod__WEBPACK_IMPORTED_MODULE_2__.z[\"enum\"]([\n        'day',\n        'week',\n        'month',\n        'provider',\n        'model'\n    ]).optional().default('day')\n});\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const queryParams = Object.fromEntries(searchParams.entries());\n        // Validate query parameters\n        const validatedParams = AnalyticsQuerySchema.parse(queryParams);\n        const { startDate, endDate, customApiConfigId, groupBy } = validatedParams;\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Check authentication\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('Authentication failed in /api/analytics/summary:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized: You must be logged in to view analytics.'\n            }, {\n                status: 401\n            });\n        }\n        // Build base query\n        let query = supabase.from('request_logs').select(`\n        request_timestamp,\n        status_code,\n        cost,\n        input_tokens,\n        output_tokens,\n        llm_provider_name,\n        llm_model_name,\n        custom_api_config_id\n      `).eq('user_id', user.id) // Filter by authenticated user\n        .not('cost', 'is', null); // Only include records with cost data\n        // Apply filters\n        if (startDate) {\n            query = query.gte('request_timestamp', startDate);\n        }\n        if (endDate) {\n            query = query.lte('request_timestamp', endDate);\n        }\n        if (customApiConfigId) {\n            query = query.eq('custom_api_config_id', customApiConfigId);\n        }\n        const { data: logs, error } = await query.order('request_timestamp', {\n            ascending: false\n        });\n        if (error) {\n            console.error('[Analytics API Error]', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch analytics data',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Calculate summary statistics\n        const totalRequests = logs.length;\n        const successfulRequests = logs.filter((log)=>log.status_code && log.status_code < 400).length;\n        const totalCost = logs.reduce((sum, log)=>sum + (log.cost || 0), 0);\n        const totalInputTokens = logs.reduce((sum, log)=>sum + (log.input_tokens || 0), 0);\n        const totalOutputTokens = logs.reduce((sum, log)=>sum + (log.output_tokens || 0), 0);\n        // Group data based on groupBy parameter\n        let groupedData = [];\n        if (groupBy === 'provider') {\n            const providerStats = logs.reduce((acc, log)=>{\n                const provider = log.llm_provider_name || 'Unknown';\n                if (!acc[provider]) {\n                    acc[provider] = {\n                        name: provider,\n                        requests: 0,\n                        cost: 0,\n                        input_tokens: 0,\n                        output_tokens: 0,\n                        success_rate: 0\n                    };\n                }\n                acc[provider].requests += 1;\n                acc[provider].cost += log.cost || 0;\n                acc[provider].input_tokens += log.input_tokens || 0;\n                acc[provider].output_tokens += log.output_tokens || 0;\n                if (log.status_code && log.status_code < 400) {\n                    acc[provider].success_rate += 1;\n                }\n                return acc;\n            }, {});\n            groupedData = Object.values(providerStats).map((provider)=>({\n                    ...provider,\n                    success_rate: provider.requests > 0 ? provider.success_rate / provider.requests * 100 : 0\n                }));\n        } else if (groupBy === 'model') {\n            const modelStats = logs.reduce((acc, log)=>{\n                const model = log.llm_model_name || 'Unknown';\n                if (!acc[model]) {\n                    acc[model] = {\n                        name: model,\n                        requests: 0,\n                        cost: 0,\n                        input_tokens: 0,\n                        output_tokens: 0,\n                        provider: log.llm_provider_name || 'Unknown'\n                    };\n                }\n                acc[model].requests += 1;\n                acc[model].cost += log.cost || 0;\n                acc[model].input_tokens += log.input_tokens || 0;\n                acc[model].output_tokens += log.output_tokens || 0;\n                return acc;\n            }, {});\n            groupedData = Object.values(modelStats);\n        } else {\n            // Group by time period (day, week, month)\n            const timeStats = logs.reduce((acc, log)=>{\n                const date = new Date(log.request_timestamp);\n                let key;\n                if (groupBy === 'day') {\n                    key = date.toISOString().split('T')[0]; // YYYY-MM-DD\n                } else if (groupBy === 'week') {\n                    const weekStart = new Date(date);\n                    weekStart.setDate(date.getDate() - date.getDay());\n                    key = weekStart.toISOString().split('T')[0];\n                } else {\n                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n                }\n                if (!acc[key]) {\n                    acc[key] = {\n                        period: key,\n                        requests: 0,\n                        cost: 0,\n                        input_tokens: 0,\n                        output_tokens: 0\n                    };\n                }\n                acc[key].requests += 1;\n                acc[key].cost += log.cost || 0;\n                acc[key].input_tokens += log.input_tokens || 0;\n                acc[key].output_tokens += log.output_tokens || 0;\n                return acc;\n            }, {});\n            groupedData = Object.values(timeStats).sort((a, b)=>a.period.localeCompare(b.period));\n        }\n        const response = {\n            summary: {\n                total_requests: totalRequests,\n                successful_requests: successfulRequests,\n                success_rate: totalRequests > 0 ? successfulRequests / totalRequests * 100 : 0,\n                total_cost: totalCost,\n                total_input_tokens: totalInputTokens,\n                total_output_tokens: totalOutputTokens,\n                total_tokens: totalInputTokens + totalOutputTokens,\n                average_cost_per_request: totalRequests > 0 ? totalCost / totalRequests : 0\n            },\n            grouped_data: groupedData,\n            filters: {\n                start_date: startDate,\n                end_date: endDate,\n                custom_api_config_id: customApiConfigId,\n                group_by: groupBy\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('[Analytics API Error]', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid query parameters',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/summary/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();