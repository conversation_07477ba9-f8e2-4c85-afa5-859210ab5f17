/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/tools/google/authorize/route";
exports.ids = ["app/api/auth/tools/google/authorize/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_auth_tools_google_authorize_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/tools/google/authorize/route.ts */ \"(rsc)/./src/app/api/auth/tools/google/authorize/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/tools/google/authorize/route\",\n        pathname: \"/api/auth/tools/google/authorize\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/tools/google/authorize/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\auth\\\\tools\\\\google\\\\authorize\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_auth_tools_google_authorize_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/tools/google/authorize/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/auth/tools/google/authorize/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/oauth/config */ \"(rsc)/./src/lib/oauth/config.ts\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n// Google OAuth Authorization Endpoint\n// Initiates OAuth flow for Google tools (Drive, Docs, Sheets, Gmail, Calendar, YouTube)\n\n\n\n\nasync function GET(request) {\n    try {\n        console.log('🔐 GOOGLE OAUTH: Authorization request started');\n        // Get authenticated user\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('🔐 GOOGLE OAUTH: User not authenticated:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Get tool type from query params\n        const { searchParams } = new URL(request.url);\n        const toolType = searchParams.get('tool');\n        const returnUrl = searchParams.get('returnUrl') || '/manual-build';\n        if (!toolType) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Tool type is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate tool type\n        const validGoogleTools = [\n            'google_drive',\n            'google_docs',\n            'google_sheets',\n            'gmail',\n            'calendar',\n            'youtube'\n        ];\n        if (!validGoogleTools.includes(toolType)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid Google tool type'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`🔐 GOOGLE OAUTH: Authorizing ${toolType} for user ${user.id}`);\n        // Generate state parameter for CSRF protection\n        const state = crypto__WEBPACK_IMPORTED_MODULE_3___default().randomBytes(32).toString('hex');\n        const stateData = {\n            userId: user.id,\n            toolType,\n            returnUrl,\n            timestamp: Date.now()\n        };\n        // Store state in session/database for verification\n        const { error: stateError } = await supabase.from('oauth_states').insert({\n            state,\n            user_id: user.id,\n            tool_type: toolType,\n            return_url: returnUrl,\n            expires_at: new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes\n        });\n        if (stateError) {\n            console.error('🔐 GOOGLE OAUTH: Error storing state:', stateError);\n        // Continue anyway, state verification is optional\n        }\n        // Generate authorization URL\n        const authUrl = (0,_lib_oauth_config__WEBPACK_IMPORTED_MODULE_2__.generateAuthUrl)(toolType, state);\n        if (!authUrl) {\n            console.error('🔐 GOOGLE OAUTH: Failed to generate auth URL');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'OAuth configuration error'\n            }, {\n                status: 500\n            });\n        }\n        console.log('🔐 GOOGLE OAUTH: Redirecting to Google authorization');\n        // Return the authorization URL for client-side redirect\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            authUrl,\n            state,\n            toolType\n        });\n    } catch (error) {\n        console.error('🔐 GOOGLE OAUTH: Authorization error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'OAuth authorization failed'\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle POST requests for programmatic authorization\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { toolType, returnUrl } = body;\n        // Create a new request with query parameters\n        const url = new URL(request.url);\n        url.searchParams.set('tool', toolType);\n        if (returnUrl) {\n            url.searchParams.set('returnUrl', returnUrl);\n        }\n        const newRequest = new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(url, {\n            method: 'GET',\n            headers: request.headers\n        });\n        return GET(newRequest);\n    } catch (error) {\n        console.error('🔐 GOOGLE OAUTH: POST authorization error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Invalid request body'\n        }, {\n            status: 400\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/tools/google/authorize/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/oauth/config.ts":
/*!*********************************!*\
  !*** ./src/lib/oauth/config.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_DESCRIPTIONS: () => (/* binding */ TOOL_DESCRIPTIONS),\n/* harmony export */   TOOL_DISPLAY_NAMES: () => (/* binding */ TOOL_DISPLAY_NAMES),\n/* harmony export */   TOOL_EMOJIS: () => (/* binding */ TOOL_EMOJIS),\n/* harmony export */   TOOL_ICONS: () => (/* binding */ TOOL_ICONS),\n/* harmony export */   generateAuthUrl: () => (/* binding */ generateAuthUrl),\n/* harmony export */   getOAuthConfigForTool: () => (/* binding */ getOAuthConfigForTool),\n/* harmony export */   getToolOAuthConfigs: () => (/* binding */ getToolOAuthConfigs),\n/* harmony export */   validateOAuthConfig: () => (/* binding */ validateOAuthConfig)\n/* harmony export */ });\n// OAuth Configuration for Tool Integrations\n// This file contains OAuth configurations for all supported tools\n// Get the base URL for redirects\nconst getBaseUrl = ()=>{\n    if (false) {}\n    // Server-side detection\n    if (false) {}\n    return process.env.NEXTAUTH_URL || 'http://localhost:3000';\n};\n// Google OAuth configuration for tools\nconst getGoogleToolsConfig = ()=>({\n        clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',\n        clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',\n        authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n        tokenUrl: 'https://oauth2.googleapis.com/token',\n        scopes: [\n            'https://www.googleapis.com/auth/drive',\n            'https://www.googleapis.com/auth/documents',\n            'https://www.googleapis.com/auth/spreadsheets',\n            'https://www.googleapis.com/auth/gmail.modify',\n            'https://www.googleapis.com/auth/calendar',\n            'https://www.googleapis.com/auth/youtube'\n        ],\n        redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/google/callback`,\n        additionalParams: {\n            access_type: 'offline',\n            prompt: 'consent',\n            include_granted_scopes: 'true'\n        }\n    });\n// Notion OAuth configuration\nconst getNotionConfig = ()=>({\n        clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',\n        clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',\n        authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',\n        tokenUrl: 'https://api.notion.com/v1/oauth/token',\n        scopes: [],\n        redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || `${getBaseUrl()}/api/auth/tools/notion/callback`,\n        additionalParams: {\n            owner: 'user',\n            response_type: 'code'\n        }\n    });\n// Tool-specific OAuth configurations\nconst getToolOAuthConfigs = ()=>{\n    const googleConfig = getGoogleToolsConfig();\n    return {\n        // Google services all use the same OAuth config with different scopes\n        google_drive: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/drive'\n            ]\n        },\n        google_docs: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/documents',\n                'https://www.googleapis.com/auth/drive.readonly'\n            ]\n        },\n        google_sheets: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/spreadsheets'\n            ]\n        },\n        gmail: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/gmail.modify'\n            ]\n        },\n        calendar: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/calendar'\n            ]\n        },\n        youtube: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/youtube'\n            ]\n        },\n        notion: getNotionConfig(),\n        supabase: {\n            clientId: '',\n            clientSecret: '',\n            authorizationUrl: '',\n            tokenUrl: '',\n            scopes: [],\n            redirectUri: '',\n            additionalParams: {}\n        }\n    };\n};\n// Get OAuth config for a specific tool\nconst getOAuthConfigForTool = (toolType)=>{\n    const configs = getToolOAuthConfigs();\n    return configs[toolType] || null;\n};\n// Validate OAuth configuration\nconst validateOAuthConfig = (config)=>{\n    return !!(config.clientId && config.clientSecret && config.authorizationUrl && config.tokenUrl && config.redirectUri);\n};\n// Generate OAuth authorization URL\nconst generateAuthUrl = (toolType, state)=>{\n    const config = getOAuthConfigForTool(toolType);\n    if (!config || !validateOAuthConfig(config)) {\n        return null;\n    }\n    const params = new URLSearchParams({\n        client_id: config.clientId,\n        redirect_uri: config.redirectUri,\n        response_type: 'code',\n        scope: config.scopes.join(' '),\n        state,\n        ...config.additionalParams\n    });\n    return `${config.authorizationUrl}?${params.toString()}`;\n};\n// Tool display names\nconst TOOL_DISPLAY_NAMES = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    google_sheets: 'Google Sheets',\n    gmail: 'Gmail',\n    calendar: 'Google Calendar',\n    youtube: 'YouTube',\n    notion: 'Notion',\n    supabase: 'Supabase'\n};\n// Tool icons - Professional logos from reliable sources\nconst TOOL_ICONS = {\n    google_drive: 'https://cloud.gmelius.com/public/logos/google/Google_Drive_Logo.svg',\n    google_docs: 'https://cloud.gmelius.com/public/logos/google/Google_Docs_Logo.svg',\n    google_sheets: 'https://cloud.gmelius.com/public/logos/google/Google_Sheets_Logo.svg',\n    gmail: 'https://cloud.gmelius.com/public/logos/google/Gmail_Logo.svg',\n    calendar: 'https://cloud.gmelius.com/public/logos/google/Google_Calendar_Logo.svg',\n    youtube: 'https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg',\n    notion: 'https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png',\n    supabase: 'https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/supabase.svg'\n};\n// Tool emoji fallbacks for places where images can't be used (like select options)\nconst TOOL_EMOJIS = {\n    google_drive: '📁',\n    google_docs: '📄',\n    google_sheets: '📊',\n    gmail: '📧',\n    calendar: '📅',\n    youtube: '📺',\n    notion: '📝',\n    supabase: '🗄️'\n};\n// Tool descriptions\nconst TOOL_DESCRIPTIONS = {\n    google_drive: 'Access and manage Google Drive files',\n    google_docs: 'Create and edit Google Documents',\n    google_sheets: 'Work with Google Spreadsheets',\n    gmail: 'Send and manage emails',\n    calendar: 'Manage calendar events and schedules',\n    youtube: 'Access YouTube data and analytics',\n    notion: 'Access Notion databases and pages',\n    supabase: 'Direct database operations'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/oauth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&page=%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ftools%2Fgoogle%2Fauthorize%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();